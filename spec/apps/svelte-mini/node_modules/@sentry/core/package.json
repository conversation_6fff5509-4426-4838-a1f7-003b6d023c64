{"name": "@sentry/core", "version": "7.120.3", "description": "Base implementation for all Sentry JavaScript SDKs", "repository": "git://github.com/getsentry/sentry-javascript.git", "homepage": "https://github.com/getsentry/sentry-javascript/tree/master/packages/core", "author": "Sentry", "license": "MIT", "engines": {"node": ">=8"}, "files": ["cjs", "esm", "types", "types-ts3.8"], "main": "cjs/index.js", "module": "esm/index.js", "types": "types/index.d.ts", "typesVersions": {"<4.9": {"types/index.d.ts": ["types-ts3.8/index.d.ts"]}}, "publishConfig": {"access": "public", "tag": "v7"}, "dependencies": {"@sentry/types": "7.120.3", "@sentry/utils": "7.120.3"}, "madge": {"detectiveOptions": {"ts": {"skipTypeImports": true}}}, "sideEffects": false}