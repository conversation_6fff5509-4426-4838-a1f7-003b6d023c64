import { MeasurementUnit, Span } from '@sentry/types';
import { MetricSummary } from '@sentry/types';
import { Primitive } from '@sentry/types';
import { MetricType } from './types';
/**
 * Fetches the metric summary if it exists for the passed span
 */
export declare function getMetricSummaryJsonForSpan(span: Span): Record<string, Array<MetricSummary>> | undefined;
/**
 * Updates the metric summary on the currently active span
 */
export declare function updateMetricSummaryOnActiveSpan(metricType: MetricType, sanitizedName: string, value: number, unit: MeasurementUnit, tags: Record<string, Primitive>, bucketKey: string): void;
//# sourceMappingURL=metric-summary.d.ts.map
