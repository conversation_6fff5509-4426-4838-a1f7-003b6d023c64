{"version": 3, "file": "api.js", "sources": ["../../src/api.ts"], "sourcesContent": ["import type { ClientOptions, DsnComponents, Dsn<PERSON>ike, SdkInfo } from '@sentry/types';\nimport { dsnToString, makeDsn, urlEncode } from '@sentry/utils';\n\nconst SENTRY_API_VERSION = '7';\n\n/** Returns the prefix to construct Sentry ingestion API endpoints. */\nfunction getBaseApiEndpoint(dsn: DsnComponents): string {\n  const protocol = dsn.protocol ? `${dsn.protocol}:` : '';\n  const port = dsn.port ? `:${dsn.port}` : '';\n  return `${protocol}//${dsn.host}${port}${dsn.path ? `/${dsn.path}` : ''}/api/`;\n}\n\n/** Returns the ingest API endpoint for target. */\nfunction _getIngestEndpoint(dsn: DsnComponents): string {\n  return `${getBaseApiEndpoint(dsn)}${dsn.projectId}/envelope/`;\n}\n\n/** Returns a URL-encoded string with auth config suitable for a query string. */\nfunction _encodedAuth(dsn: DsnComponents, sdkInfo: SdkInfo | undefined): string {\n  return urlEncode({\n    // We send only the minimum set of required information. See\n    // https://github.com/getsentry/sentry-javascript/issues/2572.\n    sentry_key: dsn.publicKey,\n    sentry_version: SENTRY_API_VERSION,\n    ...(sdkInfo && { sentry_client: `${sdkInfo.name}/${sdkInfo.version}` }),\n  });\n}\n\n/**\n * Returns the envelope endpoint URL with auth in the query string.\n *\n * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n */\nexport function getEnvelopeEndpointWithUrlEncodedAuth(\n  dsn: DsnComponents,\n  // TODO (v8): Remove `tunnelOrOptions` in favor of `options`, and use the substitute code below\n  // options: ClientOptions = {} as ClientOptions,\n  tunnelOrOptions: string | ClientOptions = {} as ClientOptions,\n): string {\n  // TODO (v8): Use this code instead\n  // const { tunnel, _metadata = {} } = options;\n  // return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, _metadata.sdk)}`;\n\n  const tunnel = typeof tunnelOrOptions === 'string' ? tunnelOrOptions : tunnelOrOptions.tunnel;\n  const sdkInfo =\n    typeof tunnelOrOptions === 'string' || !tunnelOrOptions._metadata ? undefined : tunnelOrOptions._metadata.sdk;\n\n  return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, sdkInfo)}`;\n}\n\n/** Returns the url to the report dialog endpoint. */\nexport function getReportDialogEndpoint(\n  dsnLike: DsnLike,\n  dialogOptions: {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    [key: string]: any;\n    user?: { name?: string; email?: string };\n  },\n): string {\n  const dsn = makeDsn(dsnLike);\n  if (!dsn) {\n    return '';\n  }\n\n  const endpoint = `${getBaseApiEndpoint(dsn)}embed/error-page/`;\n\n  let encodedOptions = `dsn=${dsnToString(dsn)}`;\n  for (const key in dialogOptions) {\n    if (key === 'dsn') {\n      continue;\n    }\n\n    if (key === 'onClose') {\n      continue;\n    }\n\n    if (key === 'user') {\n      const user = dialogOptions.user;\n      if (!user) {\n        continue;\n      }\n      if (user.name) {\n        encodedOptions += `&name=${encodeURIComponent(user.name)}`;\n      }\n      if (user.email) {\n        encodedOptions += `&email=${encodeURIComponent(user.email)}`;\n      }\n    } else {\n      encodedOptions += `&${encodeURIComponent(key)}=${encodeURIComponent(dialogOptions[key] as string)}`;\n    }\n  }\n\n  return `${endpoint}?${encodedOptions}`;\n}\n"], "names": [], "mappings": ";;AAGA,MAAM,kBAAA,GAAqB,GAAG,CAAA;AAC9B;AACA;AACA,SAAS,kBAAkB,CAAC,GAAG,EAAyB;AACxD,EAAE,MAAM,WAAW,GAAG,CAAC,QAAS,GAAE,CAAC,EAAA,GAAA,CAAA,QAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AACA,EAAA,MAAA,IAAA,GAAA,GAAA,CAAA,IAAA,GAAA,CAAA,CAAA,EAAA,GAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AACA,EAAA,OAAA,CAAA,EAAA,QAAA,CAAA,EAAA,EAAA,GAAA,CAAA,IAAA,CAAA,EAAA,IAAA,CAAA,EAAA,GAAA,CAAA,IAAA,GAAA,CAAA,CAAA,EAAA,GAAA,CAAA,IAAA,CAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,kBAAA,CAAA,GAAA,EAAA;AACA,EAAA,OAAA,CAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,EAAA,GAAA,CAAA,SAAA,CAAA,UAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,YAAA,CAAA,GAAA,EAAA,OAAA,EAAA;AACA,EAAA,OAAA,SAAA,CAAA;AACA;AACA;AACA,IAAA,UAAA,EAAA,GAAA,CAAA,SAAA;AACA,IAAA,cAAA,EAAA,kBAAA;AACA,IAAA,IAAA,OAAA,IAAA,EAAA,aAAA,EAAA,CAAA,EAAA,OAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qCAAA;AACA,EAAA,GAAA;AACA;AACA;AACA,EAAA,eAAA,GAAA,EAAA;AACA,EAAA;AACA;AACA;AACA;AACA;AACA,EAAA,MAAA,MAAA,GAAA,OAAA,eAAA,KAAA,QAAA,GAAA,eAAA,GAAA,eAAA,CAAA,MAAA,CAAA;AACA,EAAA,MAAA,OAAA;AACA,IAAA,OAAA,eAAA,KAAA,QAAA,IAAA,CAAA,eAAA,CAAA,SAAA,GAAA,SAAA,GAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA;AACA;AACA,EAAA,OAAA,MAAA,GAAA,MAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,YAAA,CAAA,GAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,uBAAA;AACA,EAAA,OAAA;AACA,EAAA,aAAA;;AAIA;AACA,EAAA;AACA,EAAA,MAAA,GAAA,GAAA,OAAA,CAAA,OAAA,CAAA,CAAA;AACA,EAAA,IAAA,CAAA,GAAA,EAAA;AACA,IAAA,OAAA,EAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,QAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,iBAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,cAAA,GAAA,CAAA,IAAA,EAAA,WAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,KAAA,MAAA,GAAA,IAAA,aAAA,EAAA;AACA,IAAA,IAAA,GAAA,KAAA,KAAA,EAAA;AACA,MAAA,SAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,GAAA,KAAA,SAAA,EAAA;AACA,MAAA,SAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,GAAA,KAAA,MAAA,EAAA;AACA,MAAA,MAAA,IAAA,GAAA,aAAA,CAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAA,IAAA,EAAA;AACA,QAAA,SAAA;AACA,OAAA;AACA,MAAA,IAAA,IAAA,CAAA,IAAA,EAAA;AACA,QAAA,cAAA,IAAA,CAAA,MAAA,EAAA,kBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,MAAA,IAAA,IAAA,CAAA,KAAA,EAAA;AACA,QAAA,cAAA,IAAA,CAAA,OAAA,EAAA,kBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA,MAAA;AACA,MAAA,cAAA,IAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,CAAA,EAAA,QAAA,CAAA,CAAA,EAAA,cAAA,CAAA,CAAA,CAAA;AACA;;;;"}