{"version": 3, "file": "span.js", "sources": ["../../src/span.ts"], "sourcesContent": ["import type { DsnComponents, SpanEnvelope, SpanItem } from '@sentry/types';\nimport type { Span } from '@sentry/types';\nimport { createEnvelope, dsnToString } from '@sentry/utils';\n\n/**\n * Create envelope from Span item.\n */\nexport function createSpanEnvelope(spans: Span[], dsn?: DsnComponents): SpanEnvelope {\n  const headers: SpanEnvelope[0] = {\n    sent_at: new Date().toISOString(),\n  };\n\n  if (dsn) {\n    headers.dsn = dsnToString(dsn);\n  }\n\n  const items = spans.map(createSpanItem);\n  return createEnvelope<SpanEnvelope>(headers, items);\n}\n\nfunction createSpanItem(span: Span): SpanItem {\n  const spanHeaders: SpanItem[0] = {\n    type: 'span',\n  };\n  return [spanHeaders, span];\n}\n"], "names": [], "mappings": ";;AAIA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,KAAK,EAAU,GAAG,EAAgC;AACrF,EAAE,MAAM,OAAO,GAAoB;AACnC,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,GAAG,CAAA;AACH;AACA,EAAE,IAAI,GAAG,EAAE;AACX,IAAI,OAAO,CAAC,GAAA,GAAM,WAAW,CAAC,GAAG,CAAC,CAAA;AAClC,GAAE;AACF;AACA,EAAE,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;AACzC,EAAE,OAAO,cAAc,CAAe,OAAO,EAAE,KAAK,CAAC,CAAA;AACrD,CAAA;AACA;AACA,SAAS,cAAc,CAAC,IAAI,EAAkB;AAC9C,EAAE,MAAM,WAAW,GAAgB;AACnC,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG,CAAA;AACH,EAAE,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;AAC5B;;;;"}