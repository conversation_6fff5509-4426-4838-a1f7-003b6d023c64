{"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourcesContent": ["export type { ClientClass } from './sdk';\nexport type { AsyncContextStrategy, Carrier, Layer, RunWithAsyncContextOptions } from './hub';\nexport type { OfflineStore, OfflineTransportOptions } from './transports/offline';\nexport type { ServerRuntimeClientOptions } from './server-runtime-client';\nexport type { RequestDataIntegrationOptions } from './integrations/requestdata';\nexport type { IntegrationIndex } from './integration';\n\nexport * from './tracing';\nexport * from './semanticAttributes';\nexport { createEventEnvelope, createSessionEnvelope } from './envelope';\nexport {\n  addBreadcrumb,\n  captureCheckIn,\n  withMonitor,\n  captureException,\n  captureEvent,\n  captureMessage,\n  close,\n  // eslint-disable-next-line deprecation/deprecation\n  configureScope,\n  flush,\n  lastEventId,\n  // eslint-disable-next-line deprecation/deprecation\n  startTransaction,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  withScope,\n  withIsolationScope,\n  getClient,\n  isInitialized,\n  getCurrentScope,\n  startSession,\n  endSession,\n  captureSession,\n  withActiveSpan,\n} from './exports';\nexport {\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub,\n  getIsolationScope,\n  getHubFromCarrier,\n  // eslint-disable-next-line deprecation/deprecation\n  Hub,\n  // eslint-disable-next-line deprecation/deprecation\n  makeMain,\n  getMainCarrier,\n  runWithAsyncContext,\n  setHubOnCarrier,\n  ensureHubOnCarrier,\n  setAsyncContextStrategy,\n} from './hub';\nexport { makeSession, closeSession, updateSession } from './session';\nexport { SessionFlusher } from './sessionflusher';\nexport { Scope, getGlobalScope, setGlobalScope } from './scope';\nexport {\n  notifyEventProcessors,\n  // eslint-disable-next-line deprecation/deprecation\n  addGlobalEventProcessor,\n} from './eventProcessors';\nexport { getEnvelopeEndpointWithUrlEncodedAuth, getReportDialogEndpoint } from './api';\nexport { BaseClient, addEventProcessor } from './baseclient';\nexport { ServerRuntimeClient } from './server-runtime-client';\nexport { initAndBind, setCurrentClient } from './sdk';\nexport { createTransport } from './transports/base';\nexport { makeOfflineTransport } from './transports/offline';\nexport { makeMultiplexedTransport } from './transports/multiplexed';\nexport { SDK_VERSION } from './version';\nexport {\n  getIntegrationsToSetup,\n  addIntegration,\n  defineIntegration,\n  // eslint-disable-next-line deprecation/deprecation\n  convertIntegrationFnToClass,\n} from './integration';\nexport { applyScopeDataToEvent, mergeScopeData } from './utils/applyScopeDataToEvent';\nexport { prepareEvent } from './utils/prepareEvent';\nexport { createCheckInEnvelope } from './checkin';\nexport { createSpanEnvelope } from './span';\nexport { hasTracingEnabled } from './utils/hasTracingEnabled';\nexport { isSentryRequestUrl } from './utils/isSentryRequestUrl';\nexport { handleCallbackErrors } from './utils/handleCallbackErrors';\nexport { parameterize } from './utils/parameterize';\nexport { spanToTraceHeader, spanToJSON, spanIsSampled, spanToTraceContext } from './utils/spanUtils';\nexport { getRootSpan } from './utils/getRootSpan';\nexport { applySdkMetadata } from './utils/sdkMetadata';\nexport { DEFAULT_ENVIRONMENT } from './constants';\n/* eslint-disable deprecation/deprecation */\nexport { ModuleMetadata } from './integrations/metadata';\nexport { RequestData } from './integrations/requestdata';\nexport { InboundFilters } from './integrations/inboundfilters';\nexport { FunctionToString } from './integrations/functiontostring';\nexport { LinkedErrors } from './integrations/linkederrors';\n/* eslint-enable deprecation/deprecation */\nimport * as INTEGRATIONS from './integrations';\nexport { functionToStringIntegration } from './integrations/functiontostring';\nexport { inboundFiltersIntegration } from './integrations/inboundfilters';\nexport { linkedErrorsIntegration } from './integrations/linkederrors';\nexport { moduleMetadataIntegration } from './integrations/metadata';\nexport { requestDataIntegration } from './integrations/requestdata';\nexport { metrics } from './metrics/exports';\n\n/** @deprecated Import the integration function directly, e.g. `inboundFiltersIntegration()` instead of `new Integrations.InboundFilter(). */\nconst Integrations = INTEGRATIONS;\n\n// eslint-disable-next-line deprecation/deprecation\nexport { Integrations };\n"], "names": ["INTEGRATIONS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA;AACM,MAAA,YAAA,GAAeA;;;;"}