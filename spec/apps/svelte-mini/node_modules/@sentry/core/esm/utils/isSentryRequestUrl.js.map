{"version": 3, "file": "isSentryRequestUrl.js", "sources": ["../../../src/utils/isSentryRequestUrl.ts"], "sourcesContent": ["import type { Client, DsnComponents, Hub } from '@sentry/types';\n\n/**\n * Checks whether given url points to Sentry server\n * @param url url to verify\n *\n * TODO(v8): Remove Hub fallback type\n */\n// eslint-disable-next-line deprecation/deprecation\nexport function isSentryRequestUrl(url: string, hubOrClient: Hub | Client | undefined): boolean {\n  const client =\n    hubOrClient && isHub(hubOrClient)\n      ? // eslint-disable-next-line deprecation/deprecation\n        hubOrClient.getClient()\n      : hubOrClient;\n  const dsn = client && client.getDsn();\n  const tunnel = client && client.getOptions().tunnel;\n\n  return checkDsn(url, dsn) || checkTunnel(url, tunnel);\n}\n\nfunction checkTunnel(url: string, tunnel: string | undefined): boolean {\n  if (!tunnel) {\n    return false;\n  }\n\n  return removeTrailingSlash(url) === removeTrailingSlash(tunnel);\n}\n\nfunction checkDsn(url: string, dsn: DsnComponents | undefined): boolean {\n  return dsn ? url.includes(dsn.host) : false;\n}\n\nfunction removeTrailingSlash(str: string): string {\n  return str[str.length - 1] === '/' ? str.slice(0, -1) : str;\n}\n\n// eslint-disable-next-line deprecation/deprecation\nfunction isHub(hubOrClient: Hub | Client | undefined): hubOrClient is Hub {\n  // eslint-disable-next-line deprecation/deprecation\n  return (hubOrClient as Hub).getClient !== undefined;\n}\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,GAAG,EAAU,WAAW,EAAqC;AAChG,EAAE,MAAM,MAAO;AACf,IAAI,WAAY,IAAG,KAAK,CAAC,WAAW,CAAA;AACpC;AACA,QAAQ,WAAW,CAAC,SAAS,EAAC;AAC9B,QAAQ,WAAW,CAAA;AACnB,EAAE,MAAM,MAAM,MAAA,IAAU,MAAM,CAAC,MAAM,EAAE,CAAA;AACvC,EAAE,MAAM,MAAO,GAAE,MAAO,IAAG,MAAM,CAAC,UAAU,EAAE,CAAC,MAAM,CAAA;AACrD;AACA,EAAE,OAAO,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAE,IAAG,WAAW,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;AACvD,CAAA;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAU,MAAM,EAA+B;AACvE,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,OAAO,mBAAmB,CAAC,GAAG,MAAM,mBAAmB,CAAC,MAAM,CAAC,CAAA;AACjE,CAAA;AACA;AACA,SAAS,QAAQ,CAAC,GAAG,EAAU,GAAG,EAAsC;AACxE,EAAE,OAAO,GAAA,GAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,GAAE,KAAK,CAAA;AAC7C,CAAA;AACA;AACA,SAAS,mBAAmB,CAAC,GAAG,EAAkB;AAClD,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,MAAO,GAAE,CAAC,CAAA,KAAM,GAAA,GAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,GAAE,GAAG,CAAA;AAC7D,CAAA;AACA;AACA;AACA,SAAS,KAAK,CAAC,WAAW,EAAgD;AAC1E;AACA,EAAE,OAAO,CAAC,WAAA,GAAoB,SAAA,KAAc,SAAS,CAAA;AACrD;;;;"}