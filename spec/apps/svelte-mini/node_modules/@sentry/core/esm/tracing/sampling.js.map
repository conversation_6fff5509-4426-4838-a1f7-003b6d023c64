{"version": 3, "file": "sampling.js", "sources": ["../../../src/tracing/sampling.ts"], "sourcesContent": ["import type { Options, SamplingContext } from '@sentry/types';\nimport { isNaN, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE } from '../semanticAttributes';\nimport { hasTracingEnabled } from '../utils/hasTracingEnabled';\nimport { spanToJSON } from '../utils/spanUtils';\nimport type { Transaction } from './transaction';\n\n/**\n * Makes a sampling decision for the given transaction and stores it on the transaction.\n *\n * Called every time a transaction is created. Only transactions which emerge with a `sampled` value of `true` will be\n * sent to Sentry.\n *\n * This method muttes the given `transaction` and will set the `sampled` value on it.\n * It returns the same transaction, for convenience.\n */\nexport function sampleTransaction<T extends Transaction>(\n  transaction: T,\n  options: Pick<Options, 'tracesSampleRate' | 'tracesSampler' | 'enableTracing'>,\n  samplingContext: SamplingContext,\n): T {\n  // nothing to do if tracing is not enabled\n  if (!hasTracingEnabled(options)) {\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the user has forced a sampling decision by passing a `sampled` value in their transaction context, go with that\n  // eslint-disable-next-line deprecation/deprecation\n  if (transaction.sampled !== undefined) {\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, Number(transaction.sampled));\n    return transaction;\n  }\n\n  // we would have bailed already if neither `tracesSampler` nor `tracesSampleRate` nor `enableTracing` were defined, so one of these should\n  // work; prefer the hook if so\n  let sampleRate;\n  if (typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler(samplingContext);\n    transaction.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, Number(sampleRate));\n  } else if (samplingContext.parentSampled !== undefined) {\n    sampleRate = samplingContext.parentSampled;\n  } else if (typeof options.tracesSampleRate !== 'undefined') {\n    sampleRate = options.tracesSampleRate;\n    transaction.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, Number(sampleRate));\n  } else {\n    // When `enableTracing === true`, we use a sample rate of 100%\n    sampleRate = 1;\n    transaction.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, sampleRate);\n  }\n\n  // Since this is coming from the user (or from a function provided by the user), who knows what we might get. (The\n  // only valid values are booleans or numbers between 0 and 1.)\n  if (!isValidSampleRate(sampleRate)) {\n    DEBUG_BUILD && logger.warn('[Tracing] Discarding transaction because of invalid sample rate.');\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // if the function returned 0 (or false), or if `tracesSampleRate` is 0, it's a sign the transaction should be dropped\n  if (!sampleRate) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because ${\n          typeof options.tracesSampler === 'function'\n            ? 'tracesSampler returned 0 or false'\n            : 'a negative sampling decision was inherited or tracesSampleRate is set to 0'\n        }`,\n      );\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.sampled = false;\n    return transaction;\n  }\n\n  // Now we roll the dice. Math.random is inclusive of 0, but not of 1, so strict < is safe here. In case sampleRate is\n  // a boolean, the < comparison will cause it to be automatically cast to 1 if it's true and 0 if it's false.\n  // eslint-disable-next-line deprecation/deprecation\n  transaction.sampled = Math.random() < (sampleRate as number | boolean);\n\n  // if we're not going to keep it, we're done\n  // eslint-disable-next-line deprecation/deprecation\n  if (!transaction.sampled) {\n    DEBUG_BUILD &&\n      logger.log(\n        `[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(\n          sampleRate,\n        )})`,\n      );\n    return transaction;\n  }\n\n  DEBUG_BUILD &&\n    // eslint-disable-next-line deprecation/deprecation\n    logger.log(`[Tracing] starting ${transaction.op} transaction - ${spanToJSON(transaction).description}`);\n  return transaction;\n}\n\n/**\n * Checks the given sample rate to make sure it is valid type and value (a boolean, or a number between 0 and 1).\n */\nexport function isValidSampleRate(rate: unknown): boolean {\n  // we need to check NaN explicitly because it's of type 'number' and therefore wouldn't get caught by this typecheck\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  if (isNaN(rate) || !(typeof rate === 'number' || typeof rate === 'boolean')) {\n    DEBUG_BUILD &&\n      logger.warn(\n        `[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(\n          rate,\n        )} of type ${JSON.stringify(typeof rate)}.`,\n      );\n    return false;\n  }\n\n  // in case sampleRate is a boolean, it will get automatically cast to 1 if it's true and 0 if it's false\n  if (rate < 0 || rate > 1) {\n    DEBUG_BUILD &&\n      logger.warn(`[Tracing] Given sample rate is invalid. Sample rate must be between 0 and 1. Got ${rate}.`);\n    return false;\n  }\n  return true;\n}\n"], "names": [], "mappings": ";;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB;AACjC,EAAE,WAAW;AACb,EAAE,OAAO;AACT,EAAE,eAAe;AACjB,EAAK;AACL;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE;AACnC;AACA,IAAI,WAAW,CAAC,OAAQ,GAAE,KAAK,CAAA;AAC/B,IAAI,OAAO,WAAW,CAAA;AACtB,GAAE;AACF;AACA;AACA;AACA,EAAE,IAAI,WAAW,CAAC,OAAQ,KAAI,SAAS,EAAE;AACzC;AACA,IAAI,WAAW,CAAC,YAAY,CAAC,qCAAqC,EAAE,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAA;AAChG,IAAI,OAAO,WAAW,CAAA;AACtB,GAAE;AACF;AACA;AACA;AACA,EAAE,IAAI,UAAU,CAAA;AAChB,EAAE,IAAI,OAAO,OAAO,CAAC,aAAc,KAAI,UAAU,EAAE;AACnD,IAAI,aAAa,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAA;AACvD,IAAI,WAAW,CAAC,YAAY,CAAC,qCAAqC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAA;AACvF,GAAE,MAAO,IAAI,eAAe,CAAC,aAAA,KAAkB,SAAS,EAAE;AAC1D,IAAI,UAAW,GAAE,eAAe,CAAC,aAAa,CAAA;AAC9C,GAAE,MAAO,IAAI,OAAO,OAAO,CAAC,gBAAA,KAAqB,WAAW,EAAE;AAC9D,IAAI,UAAW,GAAE,OAAO,CAAC,gBAAgB,CAAA;AACzC,IAAI,WAAW,CAAC,YAAY,CAAC,qCAAqC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAA;AACvF,SAAS;AACT;AACA,IAAI,UAAA,GAAa,CAAC,CAAA;AAClB,IAAI,WAAW,CAAC,YAAY,CAAC,qCAAqC,EAAE,UAAU,CAAC,CAAA;AAC/E,GAAE;AACF;AACA;AACA;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,EAAE;AACtC,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,kEAAkE,CAAC,CAAA;AAClG;AACA,IAAI,WAAW,CAAC,OAAQ,GAAE,KAAK,CAAA;AAC/B,IAAI,OAAO,WAAW,CAAA;AACtB,GAAE;AACF;AACA;AACA,EAAE,IAAI,CAAC,UAAU,EAAE;AACnB,IAAI,WAAY;AAChB,MAAM,MAAM,CAAC,GAAG;AAChB,QAAQ,CAAC,yCAAyC;AAClD,UAAU,OAAO,OAAO,CAAC,aAAA,KAAkB,UAAA;AAC3C,cAAc,mCAAA;AACd,cAAc,4EAAA;AACd,SAAS,CAAA;AACA,OAAA,CAAA;AACA;AACA,IAAA,WAAA,CAAA,OAAA,GAAA,KAAA,CAAA;AACA,IAAA,OAAA,WAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,EAAA,WAAA,CAAA,OAAA,GAAA,IAAA,CAAA,MAAA,EAAA,IAAA,UAAA,EAAA,CAAA;AACA;AACA;AACA;AACA,EAAA,IAAA,CAAA,WAAA,CAAA,OAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,GAAA;AACA,QAAA,CAAA,iGAAA,EAAA,MAAA;AACA,UAAA,UAAA;AACA,SAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA;AACA,IAAA,OAAA,WAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,WAAA;AACA;AACA,IAAA,MAAA,CAAA,GAAA,CAAA,CAAA,mBAAA,EAAA,WAAA,CAAA,EAAA,CAAA,eAAA,EAAA,UAAA,CAAA,WAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,OAAA,WAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,iBAAA,CAAA,IAAA,EAAA;AACA;AACA;AACA,EAAA,IAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAA,IAAA,KAAA,QAAA,IAAA,OAAA,IAAA,KAAA,SAAA,CAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,IAAA;AACA,QAAA,CAAA,uGAAA,EAAA,IAAA,CAAA,SAAA;AACA,UAAA,IAAA;AACA,SAAA,CAAA,SAAA,EAAA,IAAA,CAAA,SAAA,CAAA,OAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,EAAA,IAAA,IAAA,GAAA,CAAA,IAAA,IAAA,GAAA,CAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,CAAA,iFAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,IAAA,CAAA;AACA;;;;"}