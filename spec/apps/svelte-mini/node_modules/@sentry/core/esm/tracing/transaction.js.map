{"version": 3, "file": "transaction.js", "sources": ["../../../src/tracing/transaction.ts"], "sourcesContent": ["import type {\n  Context,\n  Contexts,\n  DynamicSamplingContext,\n  MeasurementUnit,\n  SpanTimeInput,\n  Transaction as TransactionInterface,\n  TransactionContext,\n  TransactionEvent,\n  TransactionMetadata,\n} from '@sentry/types';\nimport { dropUndefinedKeys, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport type { Hub } from '../hub';\nimport { getCurrentHub } from '../hub';\nimport { getMetricSummaryJsonForSpan } from '../metrics/metric-summary';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE } from '../semanticAttributes';\nimport { spanTimeInputToSeconds, spanToJSON, spanToTraceContext } from '../utils/spanUtils';\nimport { getDynamicSamplingContextFromSpan } from './dynamicSamplingContext';\nimport { Span as SpanClass, SpanRecorder } from './span';\nimport { getCapturedScopesOnSpan } from './trace';\n\n/** JSDoc */\nexport class Transaction extends SpanClass implements TransactionInterface {\n  /**\n   * The reference to the current hub.\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public _hub: Hub;\n\n  protected _name: string;\n\n  private _contexts: Contexts;\n\n  private _trimEnd?: boolean | undefined;\n\n  // DO NOT yet remove this property, it is used in a hack for v7 backwards compatibility.\n  private _frozenDynamicSamplingContext: Readonly<Partial<DynamicSamplingContext>> | undefined;\n\n  private _metadata: Partial<TransactionMetadata>;\n\n  /**\n   * This constructor should never be called manually. Those instrumenting tracing should use\n   * `Sentry.startTransaction()`, and internal methods should use `hub.startTransaction()`.\n   * @internal\n   * @hideconstructor\n   * @hidden\n   *\n   * @deprecated Transactions will be removed in v8. Use spans instead.\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public constructor(transactionContext: TransactionContext, hub?: Hub) {\n    super(transactionContext);\n    this._contexts = {};\n\n    // eslint-disable-next-line deprecation/deprecation\n    this._hub = hub || getCurrentHub();\n\n    this._name = transactionContext.name || '';\n\n    this._metadata = {\n      // eslint-disable-next-line deprecation/deprecation\n      ...transactionContext.metadata,\n    };\n\n    this._trimEnd = transactionContext.trimEnd;\n\n    // this is because transactions are also spans, and spans have a transaction pointer\n    // TODO (v8): Replace this with another way to set the root span\n    // eslint-disable-next-line deprecation/deprecation\n    this.transaction = this;\n\n    // If Dynamic Sampling Context is provided during the creation of the transaction, we freeze it as it usually means\n    // there is incoming Dynamic Sampling Context. (Either through an incoming request, a baggage meta-tag, or other means)\n    const incomingDynamicSamplingContext = this._metadata.dynamicSamplingContext;\n    if (incomingDynamicSamplingContext) {\n      // We shallow copy this in case anything writes to the original reference of the passed in `dynamicSamplingContext`\n      this._frozenDynamicSamplingContext = { ...incomingDynamicSamplingContext };\n    }\n  }\n\n  // This sadly conflicts with the getter/setter ordering :(\n  /* eslint-disable @typescript-eslint/member-ordering */\n\n  /**\n   * Getter for `name` property.\n   * @deprecated Use `spanToJSON(span).description` instead.\n   */\n  public get name(): string {\n    return this._name;\n  }\n\n  /**\n   * Setter for `name` property, which also sets `source` as custom.\n   * @deprecated Use `updateName()` and `setMetadata()` instead.\n   */\n  public set name(newName: string) {\n    // eslint-disable-next-line deprecation/deprecation\n    this.setName(newName);\n  }\n\n  /**\n   * Get the metadata for this transaction.\n   * @deprecated Use `spanGetMetadata(transaction)` instead.\n   */\n  public get metadata(): TransactionMetadata {\n    // We merge attributes in for backwards compatibility\n    return {\n      // Defaults\n      // eslint-disable-next-line deprecation/deprecation\n      source: 'custom',\n      spanMetadata: {},\n\n      // Legacy metadata\n      ...this._metadata,\n\n      // From attributes\n      ...(this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] && {\n        source: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] as TransactionMetadata['source'],\n      }),\n      ...(this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] && {\n        sampleRate: this._attributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] as TransactionMetadata['sampleRate'],\n      }),\n    };\n  }\n\n  /**\n   * Update the metadata for this transaction.\n   * @deprecated Use `spanGetMetadata(transaction)` instead.\n   */\n  public set metadata(metadata: TransactionMetadata) {\n    this._metadata = metadata;\n  }\n\n  /* eslint-enable @typescript-eslint/member-ordering */\n\n  /**\n   * Setter for `name` property, which also sets `source` on the metadata.\n   *\n   * @deprecated Use `.updateName()` and `.setAttribute()` instead.\n   */\n  public setName(name: string, source: TransactionMetadata['source'] = 'custom'): void {\n    this._name = name;\n    this.setAttribute(SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, source);\n  }\n\n  /** @inheritdoc */\n  public updateName(name: string): this {\n    this._name = name;\n    return this;\n  }\n\n  /**\n   * Attaches SpanRecorder to the span itself\n   * @param maxlen maximum number of spans that can be recorded\n   */\n  public initSpanRecorder(maxlen: number = 1000): void {\n    // eslint-disable-next-line deprecation/deprecation\n    if (!this.spanRecorder) {\n      // eslint-disable-next-line deprecation/deprecation\n      this.spanRecorder = new SpanRecorder(maxlen);\n    }\n    // eslint-disable-next-line deprecation/deprecation\n    this.spanRecorder.add(this);\n  }\n\n  /**\n   * Set the context of a transaction event.\n   * @deprecated Use either `.setAttribute()`, or set the context on the scope before creating the transaction.\n   */\n  public setContext(key: string, context: Context | null): void {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   *\n   * @deprecated Use top-level `setMeasurement()` instead.\n   */\n  public setMeasurement(name: string, value: number, unit: MeasurementUnit = ''): void {\n    this._measurements[name] = { value, unit };\n  }\n\n  /**\n   * Store metadata on this transaction.\n   * @deprecated Use attributes or store data on the scope instead.\n   */\n  public setMetadata(newMetadata: Partial<TransactionMetadata>): void {\n    this._metadata = { ...this._metadata, ...newMetadata };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public end(endTimestamp?: SpanTimeInput): string | undefined {\n    const timestampInS = spanTimeInputToSeconds(endTimestamp);\n    const transaction = this._finishTransaction(timestampInS);\n    if (!transaction) {\n      return undefined;\n    }\n    // eslint-disable-next-line deprecation/deprecation\n    return this._hub.captureEvent(transaction);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public toContext(): TransactionContext {\n    // eslint-disable-next-line deprecation/deprecation\n    const spanContext = super.toContext();\n\n    return dropUndefinedKeys({\n      ...spanContext,\n      name: this._name,\n      trimEnd: this._trimEnd,\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public updateWithContext(transactionContext: TransactionContext): this {\n    // eslint-disable-next-line deprecation/deprecation\n    super.updateWithContext(transactionContext);\n\n    this._name = transactionContext.name || '';\n    this._trimEnd = transactionContext.trimEnd;\n\n    return this;\n  }\n\n  /**\n   * @inheritdoc\n   *\n   * @experimental\n   *\n   * @deprecated Use top-level `getDynamicSamplingContextFromSpan` instead.\n   */\n  public getDynamicSamplingContext(): Readonly<Partial<DynamicSamplingContext>> {\n    return getDynamicSamplingContextFromSpan(this);\n  }\n\n  /**\n   * Override the current hub with a new one.\n   * Used if you want another hub to finish the transaction.\n   *\n   * @internal\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public setHub(hub: Hub): void {\n    this._hub = hub;\n  }\n\n  /**\n   * Get the profile id of the transaction.\n   */\n  public getProfileId(): string | undefined {\n    if (this._contexts !== undefined && this._contexts['profile'] !== undefined) {\n      return this._contexts['profile'].profile_id as string;\n    }\n    return undefined;\n  }\n\n  /**\n   * Finish the transaction & prepare the event to send to Sentry.\n   */\n  protected _finishTransaction(endTimestamp?: number): TransactionEvent | undefined {\n    // This transaction is already finished, so we should not flush it again.\n    if (this._endTime !== undefined) {\n      return undefined;\n    }\n\n    if (!this._name) {\n      DEBUG_BUILD && logger.warn('Transaction has no name, falling back to `<unlabeled transaction>`.');\n      this._name = '<unlabeled transaction>';\n    }\n\n    // just sets the end timestamp\n    super.end(endTimestamp);\n\n    // eslint-disable-next-line deprecation/deprecation\n    const client = this._hub.getClient();\n    if (client && client.emit) {\n      client.emit('finishTransaction', this);\n    }\n\n    if (this._sampled !== true) {\n      // At this point if `sampled !== true` we want to discard the transaction.\n      DEBUG_BUILD && logger.log('[Tracing] Discarding transaction because its trace was not chosen to be sampled.');\n\n      if (client) {\n        client.recordDroppedEvent('sample_rate', 'transaction');\n      }\n\n      return undefined;\n    }\n\n    // eslint-disable-next-line deprecation/deprecation\n    const finishedSpans = this.spanRecorder\n      ? // eslint-disable-next-line deprecation/deprecation\n        this.spanRecorder.spans.filter(span => span !== this && spanToJSON(span).timestamp)\n      : [];\n\n    if (this._trimEnd && finishedSpans.length > 0) {\n      const endTimes = finishedSpans.map(span => spanToJSON(span).timestamp).filter(Boolean) as number[];\n      this._endTime = endTimes.reduce((prev, current) => {\n        return prev > current ? prev : current;\n      });\n    }\n\n    const { scope: capturedSpanScope, isolationScope: capturedSpanIsolationScope } = getCapturedScopesOnSpan(this);\n\n    // eslint-disable-next-line deprecation/deprecation\n    const { metadata } = this;\n    // eslint-disable-next-line deprecation/deprecation\n    const { source } = metadata;\n\n    const transaction: TransactionEvent = {\n      contexts: {\n        ...this._contexts,\n        // We don't want to override trace context\n        trace: spanToTraceContext(this),\n      },\n      // TODO: Pass spans serialized via `spanToJSON()` here instead in v8.\n      spans: finishedSpans,\n      start_timestamp: this._startTime,\n      // eslint-disable-next-line deprecation/deprecation\n      tags: this.tags,\n      timestamp: this._endTime,\n      transaction: this._name,\n      type: 'transaction',\n      sdkProcessingMetadata: {\n        ...metadata,\n        capturedSpanScope,\n        capturedSpanIsolationScope,\n        ...dropUndefinedKeys({\n          dynamicSamplingContext: getDynamicSamplingContextFromSpan(this),\n        }),\n      },\n      _metrics_summary: getMetricSummaryJsonForSpan(this),\n      ...(source && {\n        transaction_info: {\n          source,\n        },\n      }),\n    };\n\n    const hasMeasurements = Object.keys(this._measurements).length > 0;\n\n    if (hasMeasurements) {\n      DEBUG_BUILD &&\n        logger.log(\n          '[Measurements] Adding measurements to transaction',\n          JSON.stringify(this._measurements, undefined, 2),\n        );\n      transaction.measurements = this._measurements;\n    }\n\n    // eslint-disable-next-line deprecation/deprecation\n    DEBUG_BUILD && logger.log(`[Tracing] Finishing ${this.op} transaction: ${this._name}.`);\n\n    return transaction;\n  }\n}\n"], "names": ["SpanClass"], "mappings": ";;;;;;;;;;AAuBA;AACO,MAAM,WAAA,SAAoBA,IAAA,EAA0C;AAC3E;AACA;AACA;AACA;;AASA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,kBAAkB,EAAsB,GAAG,EAAQ;AACxE,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAA;AAC7B,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE,CAAA;AACvB;AACA;AACA,IAAI,IAAI,CAAC,IAAK,GAAE,OAAO,aAAa,EAAE,CAAA;AACtC;AACA,IAAI,IAAI,CAAC,KAAM,GAAE,kBAAkB,CAAC,IAAA,IAAQ,EAAE,CAAA;AAC9C;AACA,IAAI,IAAI,CAAC,SAAA,GAAY;AACrB;AACA,MAAM,GAAG,kBAAkB,CAAC,QAAQ;AACpC,KAAK,CAAA;AACL;AACA,IAAI,IAAI,CAAC,QAAA,GAAW,kBAAkB,CAAC,OAAO,CAAA;AAC9C;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,WAAY,GAAE,IAAI,CAAA;AAC3B;AACA;AACA;AACA,IAAI,MAAM,8BAA+B,GAAE,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAA;AAChF,IAAI,IAAI,8BAA8B,EAAE;AACxC;AACA,MAAM,IAAI,CAAC,6BAAA,GAAgC,EAAE,GAAG,gCAAgC,CAAA;AAChF,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,IAAI,GAAW;AAC5B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAA;AACrB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,IAAI,CAAC,OAAO,EAAU;AACnC;AACA,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;AACzB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,QAAQ,GAAwB;AAC7C;AACA,IAAI,OAAO;AACX;AACA;AACA,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,YAAY,EAAE,EAAE;AACtB;AACA;AACA,MAAM,GAAG,IAAI,CAAC,SAAS;AACvB;AACA;AACA,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,gCAAgC,KAAK;AAChE,QAAQ,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,gCAAgC,CAAE;AACnE,OAAO,CAAC;AACR,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,qCAAqC,KAAK;AACrE,QAAQ,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,qCAAqC,CAAE;AAC5E,OAAO,CAAC;AACR,KAAK,CAAA;AACL,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,IAAI,QAAQ,CAAC,QAAQ,EAAuB;AACrD,IAAI,IAAI,CAAC,SAAU,GAAE,QAAQ,CAAA;AAC7B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,OAAO,CAAC,IAAI,EAAU,MAAM,GAAkC,QAAQ,EAAQ;AACvF,IAAI,IAAI,CAAC,KAAM,GAAE,IAAI,CAAA;AACrB,IAAI,IAAI,CAAC,YAAY,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAA;AAC/D,GAAE;AACF;AACA;AACA,GAAS,UAAU,CAAC,IAAI,EAAgB;AACxC,IAAI,IAAI,CAAC,KAAM,GAAE,IAAI,CAAA;AACrB,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,MAAM,GAAW,IAAI,EAAQ;AACvD;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAC5B;AACA,MAAM,IAAI,CAAC,YAAa,GAAE,IAAI,YAAY,CAAC,MAAM,CAAC,CAAA;AAClD,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;AAC/B,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,UAAU,CAAC,GAAG,EAAU,OAAO,EAAwB;AAChE,IAAI,IAAI,OAAQ,KAAI,IAAI,EAAE;AAC1B;AACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;AAChC,WAAW;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAA,GAAI,OAAO,CAAA;AACnC,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,IAAI,EAAU,KAAK,EAAU,IAAI,GAAoB,EAAE,EAAQ;AACvF,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAE,GAAE,EAAE,KAAK,EAAE,IAAA,EAAM,CAAA;AAC9C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,WAAW,EAAsC;AACtE,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,WAAA,EAAa,CAAA;AAC1D,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,GAAG,CAAC,YAAY,EAAsC;AAC/D,IAAI,MAAM,YAAa,GAAE,sBAAsB,CAAC,YAAY,CAAC,CAAA;AAC7D,IAAI,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAA;AAC7D,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;AAC9C,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,SAAS,GAAuB;AACzC;AACA,IAAI,MAAM,WAAY,GAAE,KAAK,CAAC,SAAS,EAAE,CAAA;AACzC;AACA,IAAI,OAAO,iBAAiB,CAAC;AAC7B,MAAM,GAAG,WAAW;AACpB,MAAM,IAAI,EAAE,IAAI,CAAC,KAAK;AACtB,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ;AAC5B,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,kBAAkB,EAA4B;AACzE;AACA,IAAI,KAAK,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAA;AAC/C;AACA,IAAI,IAAI,CAAC,KAAM,GAAE,kBAAkB,CAAC,IAAA,IAAQ,EAAE,CAAA;AAC9C,IAAI,IAAI,CAAC,QAAA,GAAW,kBAAkB,CAAC,OAAO,CAAA;AAC9C;AACA,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,yBAAyB,GAA8C;AAChF,IAAI,OAAO,iCAAiC,CAAC,IAAI,CAAC,CAAA;AAClD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,MAAM,CAAC,GAAG,EAAa;AAChC,IAAI,IAAI,CAAC,IAAK,GAAE,GAAG,CAAA;AACnB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,YAAY,GAAuB;AAC5C,IAAI,IAAI,IAAI,CAAC,SAAA,KAAc,SAAU,IAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAE,KAAI,SAAS,EAAE;AACjF,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,UAAW,EAAA;AAClD,KAAI;AACJ,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAY,kBAAkB,CAAC,YAAY,EAAyC;AACpF;AACA,IAAI,IAAI,IAAI,CAAC,QAAS,KAAI,SAAS,EAAE;AACrC,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAM,eAAe,MAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAA;AACvG,MAAM,IAAI,CAAC,KAAM,GAAE,yBAAyB,CAAA;AAC5C,KAAI;AACJ;AACA;AACA,IAAI,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAA;AAC3B;AACA;AACA,IAAI,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA;AACxC,IAAI,IAAI,MAAA,IAAU,MAAM,CAAC,IAAI,EAAE;AAC/B,MAAM,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;AAC5C,KAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,QAAS,KAAI,IAAI,EAAE;AAChC;AACA,MAAM,eAAe,MAAM,CAAC,GAAG,CAAC,kFAAkF,CAAC,CAAA;AACnH;AACA,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,MAAM,CAAC,kBAAkB,CAAC,aAAa,EAAE,aAAa,CAAC,CAAA;AAC/D,OAAM;AACN;AACA,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ;AACA;AACA,IAAI,MAAM,aAAA,GAAgB,IAAI,CAAC,YAAA;AAC/B;AACA,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,IAAA,IAAQ,IAAK,KAAI,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,CAAA;AAC1F,QAAQ,EAAE,CAAA;AACV;AACA,IAAI,IAAI,IAAI,CAAC,QAAA,IAAY,aAAa,CAAC,MAAA,GAAS,CAAC,EAAE;AACnD,MAAM,MAAM,WAAW,aAAa,CAAC,GAAG,CAAC,QAAQ,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CAAE,EAAA;AAC7F,MAAM,IAAI,CAAC,QAAS,GAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK;AACzD,QAAQ,OAAO,IAAK,GAAE,UAAU,IAAA,GAAO,OAAO,CAAA;AAC9C,OAAO,CAAC,CAAA;AACR,KAAI;AACJ;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,iBAAiB,EAAE,cAAc,EAAE,0BAAA,EAA6B,GAAE,uBAAuB,CAAC,IAAI,CAAC,CAAA;AAClH;AACA;AACA,IAAI,MAAM,EAAE,QAAS,EAAA,GAAI,IAAI,CAAA;AAC7B;AACA,IAAI,MAAM,EAAE,MAAO,EAAA,GAAI,QAAQ,CAAA;AAC/B;AACA,IAAI,MAAM,WAAW,GAAqB;AAC1C,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAG,IAAI,CAAC,SAAS;AACzB;AACA,QAAQ,KAAK,EAAE,kBAAkB,CAAC,IAAI,CAAC;AACvC,OAAO;AACP;AACA,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,eAAe,EAAE,IAAI,CAAC,UAAU;AACtC;AACA,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,SAAS,EAAE,IAAI,CAAC,QAAQ;AAC9B,MAAM,WAAW,EAAE,IAAI,CAAC,KAAK;AAC7B,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,qBAAqB,EAAE;AAC7B,QAAQ,GAAG,QAAQ;AACnB,QAAQ,iBAAiB;AACzB,QAAQ,0BAA0B;AAClC,QAAQ,GAAG,iBAAiB,CAAC;AAC7B,UAAU,sBAAsB,EAAE,iCAAiC,CAAC,IAAI,CAAC;AACzE,SAAS,CAAC;AACV,OAAO;AACP,MAAM,gBAAgB,EAAE,2BAA2B,CAAC,IAAI,CAAC;AACzD,MAAM,IAAI,MAAA,IAAU;AACpB,QAAQ,gBAAgB,EAAE;AAC1B,UAAU,MAAM;AAChB,SAAS;AACT,OAAO,CAAC;AACR,KAAK,CAAA;AACL;AACA,IAAI,MAAM,eAAA,GAAkB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAA,GAAS,CAAC,CAAA;AACtE;AACA,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,WAAY;AAClB,QAAQ,MAAM,CAAC,GAAG;AAClB,UAAU,mDAAmD;AAC7D,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,SAAS,EAAE,CAAC,CAAC;AAC1D,SAAS,CAAA;AACT,MAAM,WAAW,CAAC,YAAA,GAAe,IAAI,CAAC,aAAa,CAAA;AACnD,KAAI;AACJ;AACA;AACA,IAAI,WAAA,IAAe,MAAM,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC3F;AACA,IAAI,OAAO,WAAW,CAAA;AACtB,GAAE;AACF;;;;"}