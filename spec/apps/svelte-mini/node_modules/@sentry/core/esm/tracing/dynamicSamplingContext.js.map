{"version": 3, "file": "dynamicSamplingContext.js", "sources": ["../../../src/tracing/dynamicSamplingContext.ts"], "sourcesContent": ["import type { Client, DynamicSamplingContext, Scope, Span, Transaction } from '@sentry/types';\nimport { dropUndefinedKeys } from '@sentry/utils';\n\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getClient, getCurrentScope } from '../exports';\nimport { getRootSpan } from '../utils/getRootSpan';\nimport { spanIsSampled, spanToJSON } from '../utils/spanUtils';\n\n/**\n * Creates a dynamic sampling context from a client.\n *\n * Dispatches the `createDsc` lifecycle hook as a side effect.\n */\nexport function getDynamicSamplingContextFromClient(\n  trace_id: string,\n  client: Client,\n  scope?: Scope,\n): DynamicSamplingContext {\n  const options = client.getOptions();\n\n  const { publicKey: public_key } = client.getDsn() || {};\n  // TODO(v8): Remove segment from User\n  // eslint-disable-next-line deprecation/deprecation\n  const { segment: user_segment } = (scope && scope.getUser()) || {};\n\n  const dsc = dropUndefinedKeys({\n    environment: options.environment || DEFAULT_ENVIRONMENT,\n    release: options.release,\n    user_segment,\n    public_key,\n    trace_id,\n  }) as DynamicSamplingContext;\n\n  client.emit && client.emit('createDsc', dsc);\n\n  return dsc;\n}\n\n/**\n * A Span with a frozen dynamic sampling context.\n */\ntype TransactionWithV7FrozenDsc = Transaction & { _frozenDynamicSamplingContext?: DynamicSamplingContext };\n\n/**\n * Creates a dynamic sampling context from a span (and client and scope)\n *\n * @param span the span from which a few values like the root span name and sample rate are extracted.\n *\n * @returns a dynamic sampling context\n */\nexport function getDynamicSamplingContextFromSpan(span: Span): Readonly<Partial<DynamicSamplingContext>> {\n  const client = getClient();\n  if (!client) {\n    return {};\n  }\n\n  // passing emit=false here to only emit later once the DSC is actually populated\n  const dsc = getDynamicSamplingContextFromClient(spanToJSON(span).trace_id || '', client, getCurrentScope());\n\n  // TODO (v8): Remove v7FrozenDsc as a Transaction will no longer have _frozenDynamicSamplingContext\n  const txn = getRootSpan(span) as TransactionWithV7FrozenDsc | undefined;\n  if (!txn) {\n    return dsc;\n  }\n\n  // TODO (v8): Remove v7FrozenDsc as a Transaction will no longer have _frozenDynamicSamplingContext\n  // For now we need to avoid breaking users who directly created a txn with a DSC, where this field is still set.\n  // @see Transaction class constructor\n  const v7FrozenDsc = txn && txn._frozenDynamicSamplingContext;\n  if (v7FrozenDsc) {\n    return v7FrozenDsc;\n  }\n\n  // TODO (v8): Replace txn.metadata with txn.attributes[]\n  // We can't do this yet because attributes aren't always set yet.\n  // eslint-disable-next-line deprecation/deprecation\n  const { sampleRate: maybeSampleRate, source } = txn.metadata;\n  if (maybeSampleRate != null) {\n    dsc.sample_rate = `${maybeSampleRate}`;\n  }\n\n  // We don't want to have a transaction name in the DSC if the source is \"url\" because URLs might contain PII\n  const jsonSpan = spanToJSON(txn);\n\n  // after JSON conversion, txn.name becomes jsonSpan.description\n  if (source && source !== 'url') {\n    dsc.transaction = jsonSpan.description;\n  }\n\n  dsc.sampled = String(spanIsSampled(txn));\n\n  client.emit && client.emit('createDsc', dsc);\n\n  return dsc;\n}\n"], "names": [], "mappings": ";;;;;;AAQA;AACA;AACA;AACA;AACA;AACO,SAAS,mCAAmC;AACnD,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAA0B;AAC1B,EAAE,MAAM,OAAQ,GAAE,MAAM,CAAC,UAAU,EAAE,CAAA;AACrC;AACA,EAAE,MAAM,EAAE,SAAS,EAAE,YAAa,GAAE,MAAM,CAAC,MAAM,EAAG,IAAG,EAAE,CAAA;AACzD;AACA;AACA,EAAE,MAAM,EAAE,OAAO,EAAE,YAAa,EAAA,GAAI,CAAC,KAAA,IAAS,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,CAAA;AACpE;AACA,EAAE,MAAM,GAAA,GAAM,iBAAiB,CAAC;AAChC,IAAI,WAAW,EAAE,OAAO,CAAC,WAAA,IAAe,mBAAmB;AAC3D,IAAI,OAAO,EAAE,OAAO,CAAC,OAAO;AAC5B,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ,GAAG,CAAE,EAAA;AACL;AACA,EAAE,MAAM,CAAC,IAAA,IAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAA;AAC9C;AACA,EAAE,OAAO,GAAG,CAAA;AACZ,CAAA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iCAAiC,CAAC,IAAI,EAAmD;AACzG,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF;AACA;AACA,EAAE,MAAM,GAAI,GAAE,mCAAmC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,QAAS,IAAG,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAA;AAC7G;AACA;AACA,EAAE,MAAM,GAAI,GAAE,WAAW,CAAC,IAAI,CAAE,EAAA;AAChC,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,OAAO,GAAG,CAAA;AACd,GAAE;AACF;AACA;AACA;AACA;AACA,EAAE,MAAM,WAAY,GAAE,OAAO,GAAG,CAAC,6BAA6B,CAAA;AAC9D,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,OAAO,WAAW,CAAA;AACtB,GAAE;AACF;AACA;AACA;AACA;AACA,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,MAAA,EAAS,GAAE,GAAG,CAAC,QAAQ,CAAA;AAC9D,EAAE,IAAI,eAAgB,IAAG,IAAI,EAAE;AAC/B,IAAI,GAAG,CAAC,WAAY,GAAE,CAAC,EAAA,eAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,EAAA,MAAA,QAAA,GAAA,UAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA;AACA,EAAA,IAAA,MAAA,IAAA,MAAA,KAAA,KAAA,EAAA;AACA,IAAA,GAAA,CAAA,WAAA,GAAA,QAAA,CAAA,WAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,GAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,CAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,GAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA,GAAA,CAAA;AACA;;;;"}