{"version": 3, "file": "checkin.js", "sources": ["../../src/checkin.ts"], "sourcesContent": ["import type {\n  CheckInEnvelope,\n  CheckInItem,\n  DsnComponents,\n  DynamicSamplingContext,\n  SdkMetadata,\n  SerializedCheckIn,\n} from '@sentry/types';\nimport { createEnvelope, dropUndefinedKeys, dsnToString } from '@sentry/utils';\n\n/**\n * Create envelope from check in item.\n */\nexport function createCheckInEnvelope(\n  checkIn: SerializedCheckIn,\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>,\n  metadata?: SdkMetadata,\n  tunnel?: string,\n  dsn?: DsnComponents,\n): CheckInEnvelope {\n  const headers: CheckInEnvelope[0] = {\n    sent_at: new Date().toISOString(),\n  };\n\n  if (metadata && metadata.sdk) {\n    headers.sdk = {\n      name: metadata.sdk.name,\n      version: metadata.sdk.version,\n    };\n  }\n\n  if (!!tunnel && !!dsn) {\n    headers.dsn = dsnToString(dsn);\n  }\n\n  if (dynamicSamplingContext) {\n    headers.trace = dropUndefinedKeys(dynamicSamplingContext) as DynamicSamplingContext;\n  }\n\n  const item = createCheckInEnvelopeItem(checkIn);\n  return createEnvelope<CheckInEnvelope>(headers, [item]);\n}\n\nfunction createCheckInEnvelopeItem(checkIn: SerializedCheckIn): CheckInItem {\n  const checkInHeaders: CheckInItem[0] = {\n    type: 'check_in',\n  };\n  return [checkInHeaders, checkIn];\n}\n"], "names": [], "mappings": ";;AAUA;AACA;AACA;AACO,SAAS,qBAAqB;AACrC,EAAE,OAAO;AACT,EAAE,sBAAsB;AACxB,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,GAAG;AACL,EAAmB;AACnB,EAAE,MAAM,OAAO,GAAuB;AACtC,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,GAAG,CAAA;AACH;AACA,EAAE,IAAI,QAAA,IAAY,QAAQ,CAAC,GAAG,EAAE;AAChC,IAAI,OAAO,CAAC,GAAA,GAAM;AAClB,MAAM,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI;AAC7B,MAAM,OAAO,EAAE,QAAQ,CAAC,GAAG,CAAC,OAAO;AACnC,KAAK,CAAA;AACL,GAAE;AACF;AACA,EAAE,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE;AACzB,IAAI,OAAO,CAAC,GAAA,GAAM,WAAW,CAAC,GAAG,CAAC,CAAA;AAClC,GAAE;AACF;AACA,EAAE,IAAI,sBAAsB,EAAE;AAC9B,IAAI,OAAO,CAAC,KAAA,GAAQ,iBAAiB,CAAC,sBAAsB,CAAE,EAAA;AAC9D,GAAE;AACF;AACA,EAAE,MAAM,IAAK,GAAE,yBAAyB,CAAC,OAAO,CAAC,CAAA;AACjD,EAAE,OAAO,cAAc,CAAkB,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;AACzD,CAAA;AACA;AACA,SAAS,yBAAyB,CAAC,OAAO,EAAkC;AAC5E,EAAE,MAAM,cAAc,GAAmB;AACzC,IAAI,IAAI,EAAE,UAAU;AACpB,GAAG,CAAA;AACH,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;AAClC;;;;"}