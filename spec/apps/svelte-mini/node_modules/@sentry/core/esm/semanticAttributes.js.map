{"version": 3, "file": "semanticAttributes.js", "sources": ["../../src/semanticAttributes.ts"], "sourcesContent": ["/**\n * Use this attribute to represent the source of a span.\n * Should be one of: custom, url, route, view, component, task, unknown\n *\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = 'sentry.source';\n\n/**\n * Use this attribute to represent the sample rate used for a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = 'sentry.sample_rate';\n\n/**\n * Use this attribute to represent the operation of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_OP = 'sentry.op';\n\n/**\n * Use this attribute to represent the origin of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = 'sentry.origin';\n\n/**\n * The id of the profile that this span occured in.\n */\nexport const SEMANTIC_ATTRIBUTE_PROFILE_ID = 'profile_id';\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACO,MAAM,gCAAiC,GAAE,gBAAe;AAC/D;AACA;AACA;AACA;AACO,MAAM,qCAAsC,GAAE,qBAAoB;AACzE;AACA;AACA;AACA;AACO,MAAM,4BAA6B,GAAE,YAAW;AACvD;AACA;AACA;AACA;AACO,MAAM,gCAAiC,GAAE,gBAAe;AAC/D;AACA;AACA;AACA;AACO,MAAM,6BAA8B,GAAE;;;;"}