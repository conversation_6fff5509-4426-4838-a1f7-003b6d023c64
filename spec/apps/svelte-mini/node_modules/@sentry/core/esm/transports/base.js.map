{"version": 3, "file": "base.js", "sources": ["../../../src/transports/base.ts"], "sourcesContent": ["import type {\n  Envelope,\n  EnvelopeItem,\n  EnvelopeItemType,\n  Event,\n  EventDropReason,\n  EventItem,\n  InternalBaseTransportOptions,\n  Transport,\n  TransportMakeRequestResponse,\n  TransportRequestExecutor,\n} from '@sentry/types';\nimport type { PromiseBuffer, RateLimits } from '@sentry/utils';\nimport {\n  SentryError,\n  createEnvelope,\n  envelopeItemTypeToDataCategory,\n  forEachEnvelopeItem,\n  isRateLimited,\n  logger,\n  makePromiseBuffer,\n  resolvedSyncPromise,\n  serializeEnvelope,\n  updateRateLimits,\n} from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\n\nexport const DEFAULT_TRANSPORT_BUFFER_SIZE = 30;\n\n/**\n * Creates an instance of a Sentry `Transport`\n *\n * @param options\n * @param makeRequest\n */\nexport function createTransport(\n  options: InternalBaseTransportOptions,\n  makeRequest: TransportRequestExecutor,\n  buffer: PromiseBuffer<void | TransportMakeRequestResponse> = makePromiseBuffer(\n    options.bufferSize || DEFAULT_TRANSPORT_BUFFER_SIZE,\n  ),\n): Transport {\n  let rateLimits: RateLimits = {};\n  const flush = (timeout?: number): PromiseLike<boolean> => buffer.drain(timeout);\n\n  function send(envelope: Envelope): PromiseLike<void | TransportMakeRequestResponse> {\n    const filteredEnvelopeItems: EnvelopeItem[] = [];\n\n    // Drop rate limited items from envelope\n    forEachEnvelopeItem(envelope, (item, type) => {\n      const dataCategory = envelopeItemTypeToDataCategory(type);\n      if (isRateLimited(rateLimits, dataCategory)) {\n        const event: Event | undefined = getEventForEnvelopeItem(item, type);\n        options.recordDroppedEvent('ratelimit_backoff', dataCategory, event);\n      } else {\n        filteredEnvelopeItems.push(item);\n      }\n    });\n\n    // Skip sending if envelope is empty after filtering out rate limited events\n    if (filteredEnvelopeItems.length === 0) {\n      return resolvedSyncPromise();\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const filteredEnvelope: Envelope = createEnvelope(envelope[0], filteredEnvelopeItems as any);\n\n    // Creates client report for each item in an envelope\n    const recordEnvelopeLoss = (reason: EventDropReason): void => {\n      forEachEnvelopeItem(filteredEnvelope, (item, type) => {\n        const event: Event | undefined = getEventForEnvelopeItem(item, type);\n        options.recordDroppedEvent(reason, envelopeItemTypeToDataCategory(type), event);\n      });\n    };\n\n    const requestTask = (): PromiseLike<void | TransportMakeRequestResponse> =>\n      makeRequest({ body: serializeEnvelope(filteredEnvelope, options.textEncoder) }).then(\n        response => {\n          // We don't want to throw on NOK responses, but we want to at least log them\n          if (response.statusCode !== undefined && (response.statusCode < 200 || response.statusCode >= 300)) {\n            DEBUG_BUILD && logger.warn(`Sentry responded with status code ${response.statusCode} to sent event.`);\n          }\n\n          rateLimits = updateRateLimits(rateLimits, response);\n          return response;\n        },\n        error => {\n          recordEnvelopeLoss('network_error');\n          throw error;\n        },\n      );\n\n    return buffer.add(requestTask).then(\n      result => result,\n      error => {\n        if (error instanceof SentryError) {\n          DEBUG_BUILD && logger.error('Skipped sending event because buffer is full.');\n          recordEnvelopeLoss('queue_overflow');\n          return resolvedSyncPromise();\n        } else {\n          throw error;\n        }\n      },\n    );\n  }\n\n  // We use this to identifify if the transport is the base transport\n  // TODO (v8): Remove this again as we'll no longer need it\n  send.__sentry__baseTransport__ = true;\n\n  return {\n    send,\n    flush,\n  };\n}\n\nfunction getEventForEnvelopeItem(item: Envelope[1][number], type: EnvelopeItemType): Event | undefined {\n  if (type !== 'event' && type !== 'transaction') {\n    return undefined;\n  }\n\n  return Array.isArray(item) ? (item as EventItem)[1] : undefined;\n}\n"], "names": [], "mappings": ";;;AA4BO,MAAM,6BAA8B,GAAE,GAAE;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe;AAC/B,EAAE,OAAO;AACT,EAAE,WAAW;AACb,EAAE,MAAM,GAAuD,iBAAiB;AAChF,IAAI,OAAO,CAAC,UAAW,IAAG,6BAA6B;AACvD,GAAG;AACH,EAAa;AACb,EAAE,IAAI,UAAU,GAAe,EAAE,CAAA;AACjC,EAAE,MAAM,KAAM,GAAE,CAAC,OAAO,KAAoC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;AACjF;AACA,EAAE,SAAS,IAAI,CAAC,QAAQ,EAA8D;AACtF,IAAI,MAAM,qBAAqB,GAAmB,EAAE,CAAA;AACpD;AACA;AACA,IAAI,mBAAmB,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK;AAClD,MAAM,MAAM,YAAa,GAAE,8BAA8B,CAAC,IAAI,CAAC,CAAA;AAC/D,MAAM,IAAI,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE;AACnD,QAAQ,MAAM,KAAK,GAAsB,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAC5E,QAAQ,OAAO,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,YAAY,EAAE,KAAK,CAAC,CAAA;AAC5E,aAAa;AACb,QAAQ,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACxC,OAAM;AACN,KAAK,CAAC,CAAA;AACN;AACA;AACA,IAAI,IAAI,qBAAqB,CAAC,MAAO,KAAI,CAAC,EAAE;AAC5C,MAAM,OAAO,mBAAmB,EAAE,CAAA;AAClC,KAAI;AACJ;AACA;AACA,IAAI,MAAM,gBAAgB,GAAa,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,qBAAA,EAA6B,CAAA;AAChG;AACA;AACA,IAAI,MAAM,kBAAA,GAAqB,CAAC,MAAM,KAA4B;AAClE,MAAM,mBAAmB,CAAC,gBAAgB,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK;AAC5D,QAAQ,MAAM,KAAK,GAAsB,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAC5E,QAAQ,OAAO,CAAC,kBAAkB,CAAC,MAAM,EAAE,8BAA8B,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,CAAA;AACvF,OAAO,CAAC,CAAA;AACR,KAAK,CAAA;AACL;AACA,IAAI,MAAM,WAAY,GAAE;AACxB,MAAM,WAAW,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,WAAW,CAAA,EAAG,CAAC,CAAC,IAAI;AAC1F,QAAQ,YAAY;AACpB;AACA,UAAU,IAAI,QAAQ,CAAC,eAAe,SAAA,KAAc,QAAQ,CAAC,UAAW,GAAE,OAAO,QAAQ,CAAC,UAAW,IAAG,GAAG,CAAC,EAAE;AAC9G,YAAY,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,QAAQ,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAA;AACjH,WAAU;AACV;AACA,UAAU,aAAa,gBAAgB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;AAC7D,UAAU,OAAO,QAAQ,CAAA;AACzB,SAAS;AACT,QAAQ,SAAS;AACjB,UAAU,kBAAkB,CAAC,eAAe,CAAC,CAAA;AAC7C,UAAU,MAAM,KAAK,CAAA;AACrB,SAAS;AACT,OAAO,CAAA;AACP;AACA,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI;AACvC,MAAM,MAAA,IAAU,MAAM;AACtB,MAAM,SAAS;AACf,QAAQ,IAAI,KAAM,YAAW,WAAW,EAAE;AAC1C,UAAU,eAAe,MAAM,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAA;AACtF,UAAU,kBAAkB,CAAC,gBAAgB,CAAC,CAAA;AAC9C,UAAU,OAAO,mBAAmB,EAAE,CAAA;AACtC,eAAe;AACf,UAAU,MAAM,KAAK,CAAA;AACrB,SAAQ;AACR,OAAO;AACP,KAAK,CAAA;AACL,GAAE;AACF;AACA;AACA;AACA,EAAE,IAAI,CAAC,yBAA0B,GAAE,IAAI,CAAA;AACvC;AACA,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,KAAK;AACT,GAAG,CAAA;AACH,CAAA;AACA;AACA,SAAS,uBAAuB,CAAC,IAAI,EAAuB,IAAI,EAAuC;AACvG,EAAE,IAAI,IAAK,KAAI,WAAW,IAAA,KAAS,aAAa,EAAE;AAClD,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAA,GAAI,CAAC,OAAmB,CAAC,CAAA,GAAI,SAAS,CAAA;AACjE;;;;"}