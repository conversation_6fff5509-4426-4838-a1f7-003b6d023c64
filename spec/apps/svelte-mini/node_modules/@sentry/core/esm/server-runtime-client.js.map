{"version": 3, "file": "server-runtime-client.js", "sources": ["../../src/server-runtime-client.ts"], "sourcesContent": ["import type {\n  BaseTransportOptions,\n  CheckIn,\n  ClientOptions,\n  DynamicSamplingContext,\n  Event,\n  EventHint,\n  MonitorConfig,\n  ParameterizedString,\n  SerializedCheckIn,\n  Severity,\n  SeverityLevel,\n  TraceContext,\n} from '@sentry/types';\nimport { eventFromMessage, eventFromUnknownInput, logger, resolvedSyncPromise, uuid4 } from '@sentry/utils';\n\nimport { BaseClient } from './baseclient';\nimport { createCheckInEnvelope } from './checkin';\nimport { DEBUG_BUILD } from './debug-build';\nimport { getClient } from './exports';\nimport { MetricsAggregator } from './metrics/aggregator';\nimport type { Scope } from './scope';\nimport { SessionFlusher } from './sessionflusher';\nimport {\n  addTracingExtensions,\n  getDynamicSamplingContextFromClient,\n  getDynamicSamplingContextFromSpan,\n} from './tracing';\nimport { getRootSpan } from './utils/getRootSpan';\nimport { spanToTraceContext } from './utils/spanUtils';\n\nexport interface ServerRuntimeClientOptions extends ClientOptions<BaseTransportOptions> {\n  platform?: string;\n  runtime?: { name: string; version?: string };\n  serverName?: string;\n}\n\n/**\n * The Sentry Server Runtime Client SDK.\n */\nexport class ServerRuntimeClient<\n  O extends ClientOptions & ServerRuntimeClientOptions = ServerRuntimeClientOptions,\n> extends BaseClient<O> {\n  protected _sessionFlusher: SessionFlusher | undefined;\n\n  /**\n   * Creates a new Edge SDK instance.\n   * @param options Configuration options for this SDK.\n   */\n  public constructor(options: O) {\n    // Server clients always support tracing\n    addTracingExtensions();\n\n    super(options);\n\n    if (options._experiments && options._experiments['metricsAggregator']) {\n      this.metricsAggregator = new MetricsAggregator(this);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromException(exception: unknown, hint?: EventHint): PromiseLike<Event> {\n    return resolvedSyncPromise(eventFromUnknownInput(getClient(), this._options.stackParser, exception, hint));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public eventFromMessage(\n    message: ParameterizedString,\n    // eslint-disable-next-line deprecation/deprecation\n    level: Severity | SeverityLevel = 'info',\n    hint?: EventHint,\n  ): PromiseLike<Event> {\n    return resolvedSyncPromise(\n      eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace),\n    );\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined {\n    // Check if the flag `autoSessionTracking` is enabled, and if `_sessionFlusher` exists because it is initialised only\n    // when the `requestHandler` middleware is used, and hence the expectation is to have SessionAggregates payload\n    // sent to the Server only when the `requestHandler` middleware is used\n    if (this._options.autoSessionTracking && this._sessionFlusher && scope) {\n      const requestSession = scope.getRequestSession();\n\n      // Necessary checks to ensure this is code block is executed only within a request\n      // Should override the status only if `requestSession.status` is `Ok`, which is its initial stage\n      if (requestSession && requestSession.status === 'ok') {\n        requestSession.status = 'errored';\n      }\n    }\n\n    return super.captureException(exception, hint, scope);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined {\n    // Check if the flag `autoSessionTracking` is enabled, and if `_sessionFlusher` exists because it is initialised only\n    // when the `requestHandler` middleware is used, and hence the expectation is to have SessionAggregates payload\n    // sent to the Server only when the `requestHandler` middleware is used\n    if (this._options.autoSessionTracking && this._sessionFlusher && scope) {\n      const eventType = event.type || 'exception';\n      const isException =\n        eventType === 'exception' && event.exception && event.exception.values && event.exception.values.length > 0;\n\n      // If the event is of type Exception, then a request session should be captured\n      if (isException) {\n        const requestSession = scope.getRequestSession();\n\n        // Ensure that this is happening within the bounds of a request, and make sure not to override\n        // Session Status if Errored / Crashed\n        if (requestSession && requestSession.status === 'ok') {\n          requestSession.status = 'errored';\n        }\n      }\n    }\n\n    return super.captureEvent(event, hint, scope);\n  }\n\n  /**\n   *\n   * @inheritdoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    if (this._sessionFlusher) {\n      this._sessionFlusher.close();\n    }\n    return super.close(timeout);\n  }\n\n  /** Method that initialises an instance of SessionFlusher on Client */\n  public initSessionFlusher(): void {\n    const { release, environment } = this._options;\n    if (!release) {\n      DEBUG_BUILD && logger.warn('Cannot initialise an instance of SessionFlusher if no release is provided!');\n    } else {\n      this._sessionFlusher = new SessionFlusher(this, {\n        release,\n        environment,\n      });\n    }\n  }\n\n  /**\n   * Create a cron monitor check in and send it to Sentry.\n   *\n   * @param checkIn An object that describes a check in.\n   * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n   * to create a monitor automatically when sending a check in.\n   */\n  public captureCheckIn(checkIn: CheckIn, monitorConfig?: MonitorConfig, scope?: Scope): string {\n    const id = 'checkInId' in checkIn && checkIn.checkInId ? checkIn.checkInId : uuid4();\n    if (!this._isEnabled()) {\n      DEBUG_BUILD && logger.warn('SDK not enabled, will not capture checkin.');\n      return id;\n    }\n\n    const options = this.getOptions();\n    const { release, environment, tunnel } = options;\n\n    const serializedCheckIn: SerializedCheckIn = {\n      check_in_id: id,\n      monitor_slug: checkIn.monitorSlug,\n      status: checkIn.status,\n      release,\n      environment,\n    };\n\n    if ('duration' in checkIn) {\n      serializedCheckIn.duration = checkIn.duration;\n    }\n\n    if (monitorConfig) {\n      serializedCheckIn.monitor_config = {\n        schedule: monitorConfig.schedule,\n        checkin_margin: monitorConfig.checkinMargin,\n        max_runtime: monitorConfig.maxRuntime,\n        timezone: monitorConfig.timezone,\n      };\n    }\n\n    const [dynamicSamplingContext, traceContext] = this._getTraceInfoFromScope(scope);\n    if (traceContext) {\n      serializedCheckIn.contexts = {\n        trace: traceContext,\n      };\n    }\n\n    const envelope = createCheckInEnvelope(\n      serializedCheckIn,\n      dynamicSamplingContext,\n      this.getSdkMetadata(),\n      tunnel,\n      this.getDsn(),\n    );\n\n    DEBUG_BUILD && logger.info('Sending checkin:', checkIn.monitorSlug, checkIn.status);\n\n    // _sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this._sendEnvelope(envelope);\n\n    return id;\n  }\n\n  /**\n   * Method responsible for capturing/ending a request session by calling `incrementSessionStatusCount` to increment\n   * appropriate session aggregates bucket\n   */\n  protected _captureRequestSession(): void {\n    if (!this._sessionFlusher) {\n      DEBUG_BUILD && logger.warn('Discarded request mode session because autoSessionTracking option was disabled');\n    } else {\n      this._sessionFlusher.incrementSessionStatusCount();\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  protected _prepareEvent(\n    event: Event,\n    hint: EventHint,\n    scope?: Scope,\n    isolationScope?: Scope,\n  ): PromiseLike<Event | null> {\n    if (this._options.platform) {\n      event.platform = event.platform || this._options.platform;\n    }\n\n    if (this._options.runtime) {\n      event.contexts = {\n        ...event.contexts,\n        runtime: (event.contexts || {}).runtime || this._options.runtime,\n      };\n    }\n\n    if (this._options.serverName) {\n      event.server_name = event.server_name || this._options.serverName;\n    }\n\n    return super._prepareEvent(event, hint, scope, isolationScope);\n  }\n\n  /** Extract trace information from scope */\n  private _getTraceInfoFromScope(\n    scope: Scope | undefined,\n  ): [dynamicSamplingContext: Partial<DynamicSamplingContext> | undefined, traceContext: TraceContext | undefined] {\n    if (!scope) {\n      return [undefined, undefined];\n    }\n\n    // eslint-disable-next-line deprecation/deprecation\n    const span = scope.getSpan();\n    if (span) {\n      const samplingContext = getRootSpan(span) ? getDynamicSamplingContextFromSpan(span) : undefined;\n      return [samplingContext, spanToTraceContext(span)];\n    }\n\n    const { traceId, spanId, parentSpanId, dsc } = scope.getPropagationContext();\n    const traceContext: TraceContext = {\n      trace_id: traceId,\n      span_id: spanId,\n      parent_span_id: parentSpanId,\n    };\n    if (dsc) {\n      return [dsc, traceContext];\n    }\n\n    return [getDynamicSamplingContextFromClient(traceId, this, scope), traceContext];\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAqCA;AACA;AACA;AACO,MAAM,mBAAmB;AAC9B;AACF,SAAU,UAAU,CAAI;;AAGxB;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,EAAK;AACjC;AACA,IAAI,oBAAoB,EAAE,CAAA;AAC1B;AACA,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA;AAClB;AACA,IAAI,IAAI,OAAO,CAAC,YAAa,IAAG,OAAO,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;AAC3E,MAAM,IAAI,CAAC,iBAAkB,GAAE,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAA;AAC1D,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,SAAS,EAAW,IAAI,EAAkC;AACtF,IAAI,OAAO,mBAAmB,CAAC,qBAAqB,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;AAC9G,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,gBAAgB;AACzB,IAAI,OAAO;AACX;AACA,IAAI,KAAK,GAA6B,MAAM;AAC5C,IAAI,IAAI;AACR,IAAwB;AACxB,IAAI,OAAO,mBAAmB;AAC9B,MAAM,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AACvG,KAAK,CAAA;AACL,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAO,IAAI,EAAc,KAAK,EAA8B;AAC/F;AACA;AACA;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAoB,IAAG,IAAI,CAAC,eAAgB,IAAG,KAAK,EAAE;AAC5E,MAAM,MAAM,cAAe,GAAE,KAAK,CAAC,iBAAiB,EAAE,CAAA;AACtD;AACA;AACA;AACA,MAAM,IAAI,cAAe,IAAG,cAAc,CAAC,MAAA,KAAW,IAAI,EAAE;AAC5D,QAAQ,cAAc,CAAC,MAAO,GAAE,SAAS,CAAA;AACzC,OAAM;AACN,KAAI;AACJ;AACA,IAAI,OAAO,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;AACzD,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAc,KAAK,EAA8B;AACzF;AACA;AACA;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAoB,IAAG,IAAI,CAAC,eAAgB,IAAG,KAAK,EAAE;AAC5E,MAAM,MAAM,SAAU,GAAE,KAAK,CAAC,IAAA,IAAQ,WAAW,CAAA;AACjD,MAAM,MAAM,WAAY;AACxB,QAAQ,SAAA,KAAc,WAAY,IAAG,KAAK,CAAC,SAAA,IAAa,KAAK,CAAC,SAAS,CAAC,MAAO,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAO,GAAE,CAAC,CAAA;AACnH;AACA;AACA,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,MAAM,cAAe,GAAE,KAAK,CAAC,iBAAiB,EAAE,CAAA;AACxD;AACA;AACA;AACA,QAAQ,IAAI,cAAe,IAAG,cAAc,CAAC,MAAA,KAAW,IAAI,EAAE;AAC9D,UAAU,cAAc,CAAC,MAAO,GAAE,SAAS,CAAA;AAC3C,SAAQ;AACR,OAAM;AACN,KAAI;AACJ;AACA,IAAI,OAAO,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;AACjD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,KAAK,CAAC,OAAO,EAAiC;AACvD,IAAI,IAAI,IAAI,CAAC,eAAe,EAAE;AAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;AAClC,KAAI;AACJ,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;AAC/B,GAAE;AACF;AACA;AACA,GAAS,kBAAkB,GAAS;AACpC,IAAI,MAAM,EAAE,OAAO,EAAE,aAAc,GAAE,IAAI,CAAC,QAAQ,CAAA;AAClD,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,eAAe,MAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAA;AAC9G,WAAW;AACX,MAAM,IAAI,CAAC,eAAgB,GAAE,IAAI,cAAc,CAAC,IAAI,EAAE;AACtD,QAAQ,OAAO;AACf,QAAQ,WAAW;AACnB,OAAO,CAAC,CAAA;AACR,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAC,OAAO,EAAW,aAAa,EAAkB,KAAK,EAAkB;AAChG,IAAI,MAAM,EAAG,GAAE,WAAY,IAAG,WAAW,OAAO,CAAC,SAAA,GAAY,OAAO,CAAC,YAAY,KAAK,EAAE,CAAA;AACxF,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AAC5B,MAAM,eAAe,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAA;AAC9E,MAAM,OAAO,EAAE,CAAA;AACf,KAAI;AACJ;AACA,IAAI,MAAM,OAAQ,GAAE,IAAI,CAAC,UAAU,EAAE,CAAA;AACrC,IAAI,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,MAAA,EAAS,GAAE,OAAO,CAAA;AACpD;AACA,IAAI,MAAM,iBAAiB,GAAsB;AACjD,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY,EAAE,OAAO,CAAC,WAAW;AACvC,MAAM,MAAM,EAAE,OAAO,CAAC,MAAM;AAC5B,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,KAAK,CAAA;AACL;AACA,IAAI,IAAI,UAAW,IAAG,OAAO,EAAE;AAC/B,MAAM,iBAAiB,CAAC,QAAA,GAAW,OAAO,CAAC,QAAQ,CAAA;AACnD,KAAI;AACJ;AACA,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,iBAAiB,CAAC,cAAA,GAAiB;AACzC,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,QAAQ,cAAc,EAAE,aAAa,CAAC,aAAa;AACnD,QAAQ,WAAW,EAAE,aAAa,CAAC,UAAU;AAC7C,QAAQ,QAAQ,EAAE,aAAa,CAAC,QAAQ;AACxC,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,MAAM,CAAC,sBAAsB,EAAE,YAAY,CAAA,GAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;AACrF,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,iBAAiB,CAAC,QAAA,GAAW;AACnC,QAAQ,KAAK,EAAE,YAAY;AAC3B,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,MAAM,QAAS,GAAE,qBAAqB;AAC1C,MAAM,iBAAiB;AACvB,MAAM,sBAAsB;AAC5B,MAAM,IAAI,CAAC,cAAc,EAAE;AAC3B,MAAM,MAAM;AACZ,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,KAAK,CAAA;AACL;AACA,IAAI,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;AACvF;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;AAChC;AACA,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAY,sBAAsB,GAAS;AAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AAC/B,MAAM,eAAe,MAAM,CAAC,IAAI,CAAC,gFAAgF,CAAC,CAAA;AAClH,WAAW;AACX,MAAM,IAAI,CAAC,eAAe,CAAC,2BAA2B,EAAE,CAAA;AACxD,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA,GAAY,aAAa;AACzB,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,KAAK;AACT,IAAI,cAAc;AAClB,IAA+B;AAC/B,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;AAChC,MAAM,KAAK,CAAC,QAAS,GAAE,KAAK,CAAC,QAAS,IAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAA;AAC/D,KAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC/B,MAAM,KAAK,CAAC,QAAA,GAAW;AACvB,QAAQ,GAAG,KAAK,CAAC,QAAQ;AACzB,QAAQ,OAAO,EAAE,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,OAAQ,IAAG,IAAI,CAAC,QAAQ,CAAC,OAAO;AACxE,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;AAClC,MAAM,KAAK,CAAC,WAAY,GAAE,KAAK,CAAC,WAAY,IAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAA;AACvE,KAAI;AACJ;AACA,IAAI,OAAO,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;AAClE,GAAE;AACF;AACA;AACA,GAAU,sBAAsB;AAChC,IAAI,KAAK;AACT,IAAmH;AACnH,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;AACnC,KAAI;AACJ;AACA;AACA,IAAI,MAAM,IAAK,GAAE,KAAK,CAAC,OAAO,EAAE,CAAA;AAChC,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,eAAA,GAAkB,WAAW,CAAC,IAAI,CAAE,GAAE,iCAAiC,CAAC,IAAI,CAAA,GAAI,SAAS,CAAA;AACrG,MAAM,OAAO,CAAC,eAAe,EAAE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAA;AACxD,KAAI;AACJ;AACA,IAAI,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,GAAA,KAAQ,KAAK,CAAC,qBAAqB,EAAE,CAAA;AAChF,IAAI,MAAM,YAAY,GAAiB;AACvC,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,cAAc,EAAE,YAAY;AAClC,KAAK,CAAA;AACL,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,OAAO,CAAC,GAAG,EAAE,YAAY,CAAC,CAAA;AAChC,KAAI;AACJ;AACA,IAAI,OAAO,CAAC,mCAAmC,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,YAAY,CAAC,CAAA;AACpF,GAAE;AACF;;;;"}