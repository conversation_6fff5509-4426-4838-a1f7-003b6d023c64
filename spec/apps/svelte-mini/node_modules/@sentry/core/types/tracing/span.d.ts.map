{"version": 3, "file": "span.d.ts", "sourceRoot": "", "sources": ["../../../src/tracing/span.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,YAAY,EACZ,YAAY,EACZ,SAAS,EACT,IAAI,IAAI,aAAa,EACrB,kBAAkB,EAClB,cAAc,EACd,WAAW,EACX,eAAe,EACf,QAAQ,EACR,UAAU,EACV,aAAa,EACb,YAAY,EACZ,WAAW,EACZ,MAAM,eAAe,CAAC;AAmBvB,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAGnD;;;;;GAKG;AACH,qBAAa,YAAY;IAChB,KAAK,EAAE,IAAI,EAAE,CAAC;IAErB,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAS;gBAEd,MAAM,GAAE,MAAa;IAKxC;;;;;OAKG;IACI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;CAQ7B;AAED;;GAEG;AACH,qBAAa,IAAK,YAAW,aAAa;IACxC;;;OAGG;IACI,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAE1C;;;OAGG;IAEI,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAEpC;;;;OAIG;IACI,YAAY,CAAC,EAAE,YAAY,CAAC;IAEnC;;;OAGG;IACI,WAAW,CAAC,EAAE,WAAW,CAAC;IAEjC;;;;;;;;OAQG;IACI,YAAY,EAAE,YAAY,CAAC;IAElC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC;IAC3B,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC;IAC1B,SAAS,CAAC,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC7C,SAAS,CAAC,QAAQ,EAAE,OAAO,GAAG,SAAS,CAAC;IACxC,SAAS,CAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACrC,SAAS,CAAC,WAAW,EAAE,cAAc,CAAC;IACtC,wDAAwD;IACxD,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC;IAC7B,sDAAsD;IACtD,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACxC,oCAAoC;IACpC,SAAS,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,MAAM,GAAG,SAAS,CAAC;IACxD,SAAS,CAAC,cAAc,CAAC,EAAE,MAAM,CAAC;IAElC,SAAS,CAAC,aAAa,EAAE,YAAY,CAAC;IAEtC,OAAO,CAAC,WAAW,CAAC,CAAS;IAE7B;;;;;;OAMG;gBACgB,WAAW,GAAE,WAAgB;IA2ChD;;;OAGG;IACH,IAAW,IAAI,IAAI,MAAM,CAExB;IAED;;;OAGG;IACH,IAAW,IAAI,CAAC,IAAI,EAAE,MAAM,EAE3B;IAED;;;OAGG;IACH,IAAW,WAAW,IAAI,MAAM,GAAG,SAAS,CAE3C;IAED;;;OAGG;IACH,IAAW,WAAW,CAAC,WAAW,EAAE,MAAM,GAAG,SAAS,EAErD;IAED;;;OAGG;IACH,IAAW,OAAO,IAAI,MAAM,CAE3B;IAED;;;OAGG;IACH,IAAW,OAAO,CAAC,OAAO,EAAE,MAAM,EAEjC;IAED;;;OAGG;IACH,IAAW,MAAM,IAAI,MAAM,CAE1B;IAED;;;OAGG;IACH,IAAW,MAAM,CAAC,MAAM,EAAE,MAAM,EAE/B;IAED;;;;OAIG;IACH,IAAW,YAAY,CAAC,MAAM,EASH,MAAM,GAAG,SAAS,AATf,EAE7B;IAED;;;;OAIG;IACH,IAAW,YAAY,IAAI,MAAM,GAAG,SAAS,CAE5C;IAED;;;OAGG;IACH,IAAW,OAAO,IAAI,OAAO,GAAG,SAAS,CAExC;IAED;;;OAGG;IACH,IAAW,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,SAAS,EAE9C;IAED;;;OAGG;IACH,IAAW,UAAU,IAAI,cAAc,CAEtC;IAED;;;OAGG;IACH,IAAW,UAAU,CAAC,UAAU,EAAE,cAAc,EAE/C;IAED;;;OAGG;IACH,IAAW,cAAc,IAAI,MAAM,CAElC;IAED;;;OAGG;IACH,IAAW,cAAc,CAAC,SAAS,EAAE,MAAM,EAE1C;IAED;;;OAGG;IACH,IAAW,YAAY,IAAI,MAAM,GAAG,SAAS,CAE5C;IAED;;;OAGG;IACH,IAAW,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,SAAS,EAElD;IAED;;;;OAIG;IACH,IAAW,MAAM,IAAI,cAAc,GAAG,MAAM,GAAG,SAAS,CAEvD;IAED;;;;OAIG;IACH,IAAW,MAAM,CAAC,MAAM,EAAE,cAAc,GAAG,MAAM,GAAG,SAAS,EAE5D;IAED;;;;OAIG;IACH,IAAW,EAAE,IAAI,MAAM,GAAG,SAAS,CAElC;IAED;;;;;OAKG;IACH,IAAW,EAAE,CAAC,EAAE,EAAE,MAAM,GAAG,SAAS,EAEnC;IAED;;;;OAIG;IACH,IAAW,MAAM,IAAI,UAAU,GAAG,SAAS,CAE1C;IAED;;;;OAIG;IACH,IAAW,MAAM,CAAC,MAAM,EAAE,UAAU,GAAG,SAAS,EAE/C;IAID,kBAAkB;IACX,WAAW,IAAI,eAAe;IASrC;;;;;OAKG;IACI,UAAU,CACf,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,WAAW,EAAE,SAAS,GAAG,SAAS,GAAG,cAAc,CAAC,CAAC,GAClG,aAAa;IAmChB;;;;;;;;OAQG;IACI,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI;IAMlD;;;;;OAKG;IAEI,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IAM7C,kBAAkB;IACX,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,SAAS,GAAG,IAAI;IAS7E,kBAAkB;IACX,aAAa,CAAC,UAAU,EAAE,cAAc,GAAG,IAAI;IAItD;;OAEG;IACI,SAAS,CAAC,KAAK,EAAE,cAAc,GAAG,IAAI;IAK7C;;;OAGG;IACI,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IAK9C;;;;OAIG;IACI,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAIlC;;OAEG;IACI,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IAKrC;;;;OAIG;IACI,SAAS,IAAI,OAAO;IAI3B;;;;OAIG;IACI,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI;IAI1C,kBAAkB;IACX,GAAG,CAAC,YAAY,CAAC,EAAE,aAAa,GAAG,IAAI;IAqB9C;;;;OAIG;IACI,aAAa,IAAI,MAAM;IAI9B;;;;OAIG;IACI,SAAS,IAAI,WAAW;IAkB/B;;;;OAIG;IACI,iBAAiB,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;IAoBxD;;;;OAIG;IACI,eAAe,IAAI,YAAY;IAItC;;;;;;;OAOG;IACI,WAAW,IAAI,QAAQ;IAqB9B,kBAAkB;IACX,WAAW,IAAI,OAAO;IAI7B;;;OAGG;IACI,MAAM,IAAI,QAAQ;IAIzB;;;;OAIG;IACH,OAAO,CAAC,QAAQ;CAyBjB"}