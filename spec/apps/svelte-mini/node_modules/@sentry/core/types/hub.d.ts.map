{"version": 3, "file": "hub.d.ts", "sourceRoot": "", "sources": ["../../src/hub.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,UAAU,EACV,cAAc,EACd,MAAM,EACN,qBAAqB,EACrB,KAAK,EACL,SAAS,EACT,KAAK,EACL,MAAM,EACN,GAAG,IAAI,YAAY,EACnB,WAAW,EACX,gBAAgB,EAChB,SAAS,EACT,OAAO,EACP,cAAc,EACd,QAAQ,EACR,aAAa,EACb,WAAW,EACX,kBAAkB,EAClB,IAAI,EACL,MAAM,eAAe,CAAC;AAavB,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAIhC;;;;;;;GAOG;AACH,eAAO,MAAM,WAAW,QAA0B,CAAC;AAQnD,MAAM,WAAW,0BAA0B;IACzC,mFAAmF;IACnF,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB;AAED;;;;GAIG;AACH,MAAM,WAAW,oBAAoB;IACnC;;OAEG;IAEH,aAAa,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC;IACrC;;OAEG;IACH,mBAAmB,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,0BAA0B,GAAG,CAAC,CAAC;CACnF;AAED;;;GAGG;AACH,MAAM,WAAW,KAAK;IACpB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,KAAK,EAAE,KAAK,CAAC;CACd;AAED;;;GAGG;AACH,MAAM,WAAW,OAAO;IACtB,UAAU,CAAC,EAAE;QAEX,GAAG,CAAC,EAAE,GAAG,CAAC;QACV,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAC3B;;WAEG;QACH,YAAY,CAAC,EAAE,WAAW,EAAE,CAAC;QAC7B,UAAU,CAAC,EAAE;YACX,iFAAiF;YAEjF,CAAC,GAAG,EAAE,MAAM,GAAG,QAAQ,CAAC;SACzB,CAAC;KACH,CAAC;CACH;AAED;;;;;;;;;;GAUG;AAEH,qBAAa,GAAI,YAAW,YAAY;IA6DpC,OAAO,CAAC,QAAQ,CAAC,QAAQ;IA5D3B,2DAA2D;IAC3D,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAU;IAEjC,uDAAuD;IACvD,OAAO,CAAC,YAAY,CAAC,CAAS;IAE9B,OAAO,CAAC,eAAe,CAAQ;IAE/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+CG;gBAED,MAAM,CAAC,EAAE,MAAM,EACf,KAAK,CAAC,EAAE,KAAK,EACb,cAAc,CAAC,EAAE,KAAK,EACL,QAAQ,GAAE,MAAoB;IA4BjD;;;;;;;OAOG;IACI,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO;IAI5C;;;;;OAKG;IACI,UAAU,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAYxC;;;;OAIG;IACI,SAAS,IAAI,KAAK;IAazB;;;;OAIG;IACI,QAAQ,IAAI,OAAO;IAO1B;;;;OAIG;IACI,SAAS,CAAC,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC;IAkCrD;;;;OAIG;IACI,SAAS,CAAC,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,SAAS;IAKnD;;;;OAIG;IACI,QAAQ,IAAI,KAAK;IAKxB;;OAEG;IACI,iBAAiB,IAAI,KAAK;IAIjC;;;OAGG;IACI,QAAQ,IAAI,KAAK,EAAE;IAI1B;;;OAGG;IACI,WAAW,IAAI,KAAK;IAI3B;;;;OAIG;IACI,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAcrE;;;;OAIG;IACI,cAAc,CACnB,OAAO,EAAE,MAAM,EAEf,KAAK,CAAC,EAAE,QAAQ,GAAG,aAAa,EAChC,IAAI,CAAC,EAAE,SAAS,GACf,MAAM;IAcT;;;;OAIG;IACI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAU3D;;;;OAIG;IACI,WAAW,IAAI,MAAM,GAAG,SAAS;IAIxC;;;;OAIG;IACI,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,GAAG,IAAI;IAkCzE;;;OAGG;IACI,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI;IAQvC;;;OAGG;IACI,OAAO,CAAC,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,IAAI;IAQxD;;;OAGG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAQtC;;;OAGG;IACI,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI;IAQlD;;;OAGG;IACI,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAQhD;;;OAGG;IAEI,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,GAAG,IAAI,GAAG,IAAI;IAQ7E;;;;OAIG;IACI,cAAc,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI;IAQ7D;;OAEG;IAEI,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI;IAW9C;;;OAGG;IACI,cAAc,CAAC,CAAC,SAAS,WAAW,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;IAaxF;;;;;;;;;;;;;;;;;;OAkBG;IACI,gBAAgB,CAAC,OAAO,EAAE,kBAAkB,EAAE,qBAAqB,CAAC,EAAE,qBAAqB,GAAG,WAAW;IAqBhH;;;OAGG;IACI,YAAY,IAAI;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE;IAIhD;;;;OAIG;IACI,cAAc,CAAC,UAAU,GAAE,OAAe,GAAG,IAAI;IAWxD;;;OAGG;IACI,UAAU,IAAI,IAAI;IAczB;;;OAGG;IACI,YAAY,CAAC,OAAO,CAAC,EAAE,cAAc,GAAG,OAAO;IA8BtD;;;;;;OAMG;IACI,oBAAoB,IAAI,OAAO;IAOtC;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAU1B;;OAEG;IAGH,OAAO,CAAC,oBAAoB;CAQ7B;AAED;;;;;;IAMI;AACJ,wBAAgB,cAAc,IAAI,OAAO,CAMxC;AAED;;;;;;GAMG;AAEH,wBAAgB,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAKtC;AAED;;;;;;;;GAQG;AAEH,wBAAgB,aAAa,IAAI,GAAG,CAcnC;AAED;;;;GAIG;AACH,wBAAgB,iBAAiB,IAAI,KAAK,CAGzC;AAmBD;;;;GAIG;AAEH,wBAAgB,kBAAkB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAE,GAAoB,GAAG,IAAI,CAgBvF;AAED;;;;GAIG;AACH,wBAAgB,uBAAuB,CAAC,QAAQ,EAAE,oBAAoB,GAAG,SAAS,GAAG,IAAI,CAKxF;AAED;;;;;;GAMG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO,GAAE,0BAA+B,GAAG,CAAC,CASrG;AAUD;;;;;GAKG;AAEH,wBAAgB,iBAAiB,CAAC,OAAO,EAAE,OAAO,GAAG,GAAG,CAGvD;AAED;;;;;GAKG;AAEH,wBAAgB,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,OAAO,CAKnE"}