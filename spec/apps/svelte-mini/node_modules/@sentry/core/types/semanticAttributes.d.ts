/**
 * Use this attribute to represent the source of a span.
 * Should be one of: custom, url, route, view, component, task, unknown
 *
 */
export declare const SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = "sentry.source";
/**
 * Use this attribute to represent the sample rate used for a span.
 */
export declare const SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = "sentry.sample_rate";
/**
 * Use this attribute to represent the operation of a span.
 */
export declare const SEMANTIC_ATTRIBUTE_SENTRY_OP = "sentry.op";
/**
 * Use this attribute to represent the origin of a span.
 */
export declare const SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = "sentry.origin";
/**
 * The id of the profile that this span occured in.
 */
export declare const SEMANTIC_ATTRIBUTE_PROFILE_ID = "profile_id";
//# sourceMappingURL=semanticAttributes.d.ts.map