{"version": 3, "file": "scope.d.ts", "sourceRoot": "", "sources": ["../../src/scope.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,UAAU,EACV,UAAU,EACV,cAAc,EACd,MAAM,EACN,OAAO,EACP,QAAQ,EACR,KAAK,EACL,SAAS,EACT,cAAc,EACd,KAAK,EACL,MAAM,EACN,SAAS,EACT,kBAAkB,EAClB,cAAc,EACd,KAAK,IAAI,cAAc,EAEvB,SAAS,EACT,OAAO,EACP,QAAQ,EACR,aAAa,EACb,IAAI,EACJ,WAAW,EACX,IAAI,EACL,MAAM,eAAe,CAAC;AAkBvB;;;GAGG;AACH,qBAAa,KAAM,YAAW,cAAc;IAC1C,sCAAsC;IACtC,SAAS,CAAC,mBAAmB,EAAE,OAAO,CAAC;IAEvC,oDAAoD;IACpD,SAAS,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,CAAC,CAAC;IAEzD,oEAAoE;IACpE,SAAS,CAAC,gBAAgB,EAAE,cAAc,EAAE,CAAC;IAE7C,4BAA4B;IAC5B,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;IAErC,WAAW;IACX,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;IAEtB,WAAW;IACX,SAAS,CAAC,KAAK,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAE9C,YAAY;IACZ,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;IAEzB,eAAe;IACf,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC;IAE9B,kBAAkB;IAClB,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC;IAErC,kDAAkD;IAClD,SAAS,CAAC,mBAAmB,EAAE,kBAAkB,CAAC;IAElD;;;OAGG;IACH,SAAS,CAAC,sBAAsB,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,CAAC;IAE7D,kBAAkB;IAClB,SAAS,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;IAElC,eAAe;IAEf,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ,GAAG,aAAa,CAAC;IAE5C;;OAEG;IACH,SAAS,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAEpC,WAAW;IACX,SAAS,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAEvB,cAAc;IACd,SAAS,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IAE7B,kCAAkC;IAClC,SAAS,CAAC,eAAe,CAAC,EAAE,cAAc,CAAC;IAE3C,+BAA+B;IAC/B,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;;IAkB3B;;;OAGG;WACW,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG,KAAK;IAIzC;;OAEG;IACI,KAAK,IAAI,KAAK;IAsBrB,sCAAsC;IAC/B,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAIlD;;;;OAIG;IACI,SAAS,IAAI,MAAM,GAAG,SAAS;IAItC;;;OAGG;IACI,gBAAgB,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI;IAI/D;;OAEG;IACI,iBAAiB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI;IAKxD;;OAEG;IACI,OAAO,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI;IAmBvC;;OAEG;IACI,OAAO,IAAI,IAAI,GAAG,SAAS;IAIlC;;OAEG;IACI,iBAAiB,IAAI,cAAc,GAAG,SAAS;IAItD;;OAEG;IACI,iBAAiB,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI;IAK/D;;OAEG;IACI,OAAO,CAAC,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,GAAG,IAAI;IASxD;;OAEG;IACI,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI;IAMlD;;OAEG;IACI,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;IAStC;;OAEG;IACI,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAMhD;;OAEG;IACI,cAAc,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,IAAI;IAMlD;;OAEG;IACI,QAAQ,CAEb,KAAK,EAAE,QAAQ,GAAG,aAAa,GAC9B,IAAI;IAMP;;OAEG;IACI,kBAAkB,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI;IAM9C;;OAEG;IACI,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI;IAY7D;;;;OAIG;IACI,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI;IAMjC;;;OAGG;IACI,OAAO,IAAI,IAAI,GAAG,SAAS;IAIlC;;;OAGG;IACI,cAAc,IAAI,WAAW,GAAG,SAAS;IAUhD;;OAEG;IACI,UAAU,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI;IAU1C;;OAEG;IACI,UAAU,IAAI,OAAO,GAAG,SAAS;IAIxC;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,GAAG,IAAI;IAqDpD;;OAEG;IACI,KAAK,IAAI,IAAI;IAkBpB;;OAEG;IACI,aAAa,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI;IAsB3E;;OAEG;IACI,iBAAiB,IAAI,UAAU,GAAG,SAAS;IAIlD;;OAEG;IACI,gBAAgB,IAAI,IAAI;IAM/B;;OAEG;IACI,aAAa,CAAC,UAAU,EAAE,UAAU,GAAG,IAAI;IAKlD;;;OAGG;IACI,cAAc,IAAI,UAAU,EAAE;IAMrC;;OAEG;IACI,gBAAgB,IAAI,IAAI;IAK/B,kBAAkB;IACX,YAAY,IAAI,SAAS;IAkChC;;;;;;;OAOG;IACI,YAAY,CACjB,KAAK,EAAE,KAAK,EACZ,IAAI,GAAE,SAAc,EACpB,yBAAyB,GAAE,cAAc,EAAO,GAC/C,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;IAc5B;;OAEG;IACI,wBAAwB,CAAC,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;KAAE,GAAG,IAAI;IAM1E;;OAEG;IACI,qBAAqB,CAAC,OAAO,EAAE,kBAAkB,GAAG,IAAI;IAK/D;;OAEG;IACI,qBAAqB,IAAI,kBAAkB;IAIlD;;;;;;OAMG;IACI,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAwBrE;;;;;;;OAOG;IACI,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAyBvF;;;;;;OAMG;IACI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,MAAM;IAa3D;;OAEG;IACH,SAAS,CAAC,qBAAqB,IAAI,IAAI;CAYxC;AAED;;;GAGG;AACH,wBAAgB,cAAc,IAAI,cAAc,CAM/C;AAED;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAAE,cAAc,GAAG,SAAS,GAAG,IAAI,CAEtE"}