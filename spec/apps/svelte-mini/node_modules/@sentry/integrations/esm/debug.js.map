{"version": 3, "file": "debug.js", "sources": ["../../../src/debug.ts"], "sourcesContent": ["import { convertIntegrationFnToClass, defineIntegration } from '@sentry/core';\nimport type { Client, Event, EventHint, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\nimport { consoleSandbox } from '@sentry/utils';\n\nconst INTEGRATION_NAME = 'Debug';\n\ninterface DebugOptions {\n  /** Controls whether console output created by this integration should be stringified. Default: `false` */\n  stringify?: boolean;\n  /** Controls whether a debugger should be launched before an event is sent. Default: `false` */\n  debugger?: boolean;\n}\n\nconst _debugIntegration = ((options: DebugOptions = {}) => {\n  const _options = {\n    debugger: false,\n    stringify: false,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    setup(client) {\n      if (!client.on) {\n        return;\n      }\n\n      client.on('beforeSendEvent', (event: Event, hint?: EventHint) => {\n        if (_options.debugger) {\n          // eslint-disable-next-line no-debugger\n          debugger;\n        }\n\n        /* eslint-disable no-console */\n        consoleSandbox(() => {\n          if (_options.stringify) {\n            console.log(JSON.stringify(event, null, 2));\n            if (hint && Object.keys(hint).length) {\n              console.log(JSON.stringify(hint, null, 2));\n            }\n          } else {\n            console.log(event);\n            if (hint && Object.keys(hint).length) {\n              console.log(hint);\n            }\n          }\n        });\n        /* eslint-enable no-console */\n      });\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const debugIntegration = defineIntegration(_debugIntegration);\n\n/**\n * Integration to debug sent Sentry events.\n * This integration should not be used in production.\n *\n * @deprecated Use `debugIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const Debug = convertIntegrationFnToClass(INTEGRATION_NAME, debugIntegration) as IntegrationClass<\n  Integration & { setup: (client: Client) => void }\n> & {\n  new (options?: {\n    stringify?: boolean;\n    debugger?: boolean;\n  }): Integration;\n};\n"], "names": [], "mappings": ";;;AAIA,MAAM,gBAAA,GAAmB,OAAO,CAAA;;AAShC,MAAM,iBAAA,IAAqB,CAAC,OAAO,GAAiB,EAAE,KAAK;AAC3D,EAAE,MAAM,WAAW;AACnB,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,SAAS,EAAE,KAAK;AACpB,IAAI,GAAG,OAAO;AACd,GAAG,CAAA;AACH;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;AACtB,QAAQ,OAAM;AACd,OAAM;AACN;AACA,MAAM,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,KAAK,EAAS,IAAI,KAAiB;AACvE,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;AAC/B;AACA,UAAU,SAAQ;AAClB,SAAQ;AACR;AACA;AACA,QAAQ,cAAc,CAAC,MAAM;AAC7B,UAAU,IAAI,QAAQ,CAAC,SAAS,EAAE;AAClC,YAAY,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;AACvD,YAAY,IAAI,IAAK,IAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;AAClD,cAAc,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAA;AACxD,aAAY;AACZ,iBAAiB;AACjB,YAAY,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;AAC9B,YAAY,IAAI,IAAK,IAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;AAClD,cAAc,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;AAC/B,aAAY;AACZ,WAAU;AACV,SAAS,CAAC,CAAA;AACV;AACA,OAAO,CAAC,CAAA;AACR,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,gBAAiB,GAAE,iBAAiB,CAAC,iBAAiB,EAAC;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,QAAQ,2BAA2B,CAAC,gBAAgB,EAAE,gBAAgB,CAAE;;;;;;"}