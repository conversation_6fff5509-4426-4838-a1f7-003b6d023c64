{"version": 3, "file": "offline.js", "sources": ["../../../src/offline.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable deprecation/deprecation */\nimport type { Event, EventProcessor, Hub, Integration } from '@sentry/types';\nimport { G<PERSON><PERSON><PERSON>L_OBJ, logger, normalize, uuid4 } from '@sentry/utils';\nimport * as localForage from 'localforage';\n\nimport { DEBUG_BUILD } from './debug-build';\n\nconst WINDOW = GLOBAL_OBJ as typeof GLOBAL_OBJ & Window;\n\ntype LocalForage = {\n  setItem<T>(key: string, value: T, callback?: (err: any, value: T) => void): Promise<T>;\n  iterate<T, U>(\n    iteratee: (value: T, key: string, iterationNumber: number) => U,\n    callback?: (err: any, result: U) => void,\n  ): Promise<U>;\n  removeItem(key: string, callback?: (err: any) => void): Promise<void>;\n  length(): Promise<number>;\n};\n\nexport type Item = { key: string; value: Event };\n\n/**\n * cache offline errors and send when connected\n * @deprecated The offline integration has been deprecated in favor of the offline transport wrapper.\n *\n * http://docs.sentry.io/platforms/javascript/configuration/transports/#offline-caching\n */\nexport class Offline implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Offline';\n\n  /**\n   * @inheritDoc\n   */\n  public readonly name: string;\n\n  /**\n   * the current hub instance\n   */\n  public hub?: Hub;\n\n  /**\n   * maximum number of events to store while offline\n   */\n  public maxStoredEvents: number;\n\n  /**\n   * event cache\n   */\n  public offlineEventStore: LocalForage;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options: { maxStoredEvents?: number } = {}) {\n    this.name = Offline.id;\n\n    this.maxStoredEvents = options.maxStoredEvents || 30; // set a reasonable default\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    this.offlineEventStore = localForage.createInstance({\n      name: 'sentry/offlineEventStore',\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(addGlobalEventProcessor: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    this.hub = getCurrentHub();\n\n    if ('addEventListener' in WINDOW) {\n      WINDOW.addEventListener('online', () => {\n        void this._sendEvents().catch(() => {\n          DEBUG_BUILD && logger.warn('could not send cached events');\n        });\n      });\n    }\n\n    const eventProcessor: EventProcessor = event => {\n      // eslint-disable-next-line deprecation/deprecation\n      if (this.hub && this.hub.getIntegration(Offline)) {\n        // cache if we are positively offline\n        if ('navigator' in WINDOW && 'onLine' in WINDOW.navigator && !WINDOW.navigator.onLine) {\n          DEBUG_BUILD && logger.log('Event dropped due to being a offline - caching instead');\n\n          void this._cacheEvent(event)\n            .then((_event: Event): Promise<void> => this._enforceMaxEvents())\n            .catch((_error): void => {\n              DEBUG_BUILD && logger.warn('could not cache event while offline');\n            });\n\n          // return null on success or failure, because being offline will still result in an error\n          return null;\n        }\n      }\n\n      return event;\n    };\n\n    eventProcessor.id = this.name;\n    addGlobalEventProcessor(eventProcessor);\n\n    // if online now, send any events stored in a previous offline session\n    if ('navigator' in WINDOW && 'onLine' in WINDOW.navigator && WINDOW.navigator.onLine) {\n      void this._sendEvents().catch(() => {\n        DEBUG_BUILD && logger.warn('could not send cached events');\n      });\n    }\n  }\n\n  /**\n   * cache an event to send later\n   * @param event an event\n   */\n  private async _cacheEvent(event: Event): Promise<Event> {\n    return this.offlineEventStore.setItem<Event>(uuid4(), normalize(event));\n  }\n\n  /**\n   * purge excess events if necessary\n   */\n  private async _enforceMaxEvents(): Promise<void> {\n    const events: Array<{ event: Event; cacheKey: string }> = [];\n\n    return this.offlineEventStore\n      .iterate<Event, void>((event: Event, cacheKey: string, _index: number): void => {\n        // aggregate events\n        events.push({ cacheKey, event });\n      })\n      .then(\n        (): Promise<void> =>\n          // this promise resolves when the iteration is finished\n          this._purgeEvents(\n            // purge all events past maxStoredEvents in reverse chronological order\n            events\n              .sort((a, b) => (b.event.timestamp || 0) - (a.event.timestamp || 0))\n              .slice(this.maxStoredEvents < events.length ? this.maxStoredEvents : events.length)\n              .map(event => event.cacheKey),\n          ),\n      )\n      .catch((_error): void => {\n        DEBUG_BUILD && logger.warn('could not enforce max events');\n      });\n  }\n\n  /**\n   * purge event from cache\n   */\n  private async _purgeEvent(cacheKey: string): Promise<void> {\n    return this.offlineEventStore.removeItem(cacheKey);\n  }\n\n  /**\n   * purge events from cache\n   */\n  private async _purgeEvents(cacheKeys: string[]): Promise<void> {\n    // trail with .then to ensure the return type as void and not void|void[]\n    return Promise.all(cacheKeys.map(cacheKey => this._purgeEvent(cacheKey))).then();\n  }\n\n  /**\n   * send all events\n   */\n  private async _sendEvents(): Promise<void> {\n    return this.offlineEventStore.iterate<Event, void>((event: Event, cacheKey: string, _index: number): void => {\n      if (this.hub) {\n        this.hub.captureEvent(event);\n\n        void this._purgeEvent(cacheKey).catch((_error): void => {\n          DEBUG_BUILD && logger.warn('could not purge event from cache');\n        });\n      } else {\n        DEBUG_BUILD && logger.warn('no hub found - could not send cached event');\n      }\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;AASA,MAAM,MAAA,GAAS,UAAW,EAAA;;AAc1B;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,SAA+B;AAC5C;AACA;AACA;AACA,GAAS,OAAA,YAAA,GAAA,CAAA,IAAA,CAAO,EAAE,GAAW,UAAS,CAAA;AACtC;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,GAAiC,EAAE,EAAE;AACjE,IAAI,IAAI,CAAC,IAAA,GAAO,OAAO,CAAC,EAAE,CAAA;AAC1B;AACA,IAAI,IAAI,CAAC,eAAgB,GAAE,OAAO,CAAC,eAAA,IAAmB,EAAE,CAAA;AACxD;AACA,IAAI,IAAI,CAAC,iBAAA,GAAoB,WAAW,CAAC,cAAc,CAAC;AACxD,MAAM,IAAI,EAAE,0BAA0B;AACtC,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,uBAAuB,EAAsC,aAAa,EAAmB;AAChH,IAAI,IAAI,CAAC,GAAA,GAAM,aAAa,EAAE,CAAA;AAC9B;AACA,IAAI,IAAI,kBAAmB,IAAG,MAAM,EAAE;AACtC,MAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM;AAC9C,QAAQ,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,MAAM;AAC5C,UAAU,eAAe,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAA;AACpE,SAAS,CAAC,CAAA;AACV,OAAO,CAAC,CAAA;AACR,KAAI;AACJ;AACA,IAAI,MAAM,cAAc,GAAmB,SAAS;AACpD;AACA,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AACxD;AACA,QAAQ,IAAI,WAAA,IAAe,MAAO,IAAG,YAAY,MAAM,CAAC,SAAA,IAAa,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;AAC/F,UAAU,eAAe,MAAM,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAA;AAC7F;AACA,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,CAAA;AACrC,aAAa,IAAI,CAAC,CAAC,MAAM,KAA2B,IAAI,CAAC,iBAAiB,EAAE,CAAA;AAC5E,aAAa,KAAK,CAAC,CAAC,MAAM,KAAW;AACrC,cAAc,eAAe,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAA;AAC/E,aAAa,CAAC,CAAA;AACd;AACA;AACA,UAAU,OAAO,IAAI,CAAA;AACrB,SAAQ;AACR,OAAM;AACN;AACA,MAAM,OAAO,KAAK,CAAA;AAClB,KAAK,CAAA;AACL;AACA,IAAI,cAAc,CAAC,EAAA,GAAK,IAAI,CAAC,IAAI,CAAA;AACjC,IAAI,uBAAuB,CAAC,cAAc,CAAC,CAAA;AAC3C;AACA;AACA,IAAI,IAAI,WAAA,IAAe,MAAA,IAAU,QAAS,IAAG,MAAM,CAAC,aAAa,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE;AAC1F,MAAM,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,MAAM;AAC1C,QAAQ,eAAe,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAA;AAClE,OAAO,CAAC,CAAA;AACR,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAU,MAAM,WAAW,CAAC,KAAK,EAAyB;AAC1D,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAQ,KAAK,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA;AAC3E,GAAE;AACF;AACA;AACA;AACA;AACA,GAAU,MAAM,iBAAiB,GAAkB;AACnD,IAAI,MAAM,MAAM,GAA8C,EAAE,CAAA;AAChE;AACA,IAAI,OAAO,IAAI,CAAC,iBAAA;AAChB,OAAO,OAAO,CAAc,CAAC,KAAK,EAAS,QAAQ,EAAU,MAAM,KAAmB;AACtF;AACA,QAAQ,MAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,KAAM,EAAC,CAAC,CAAA;AACxC,OAAO,CAAA;AACP,OAAO,IAAI;AACX,QAAQ;AACR;AACA,UAAU,IAAI,CAAC,YAAY;AAC3B;AACA,YAAY,MAAA;AACZ,eAAe,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,SAAA,IAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,SAAA,IAAa,CAAC,CAAC,CAAA;AACjF,eAAe,KAAK,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,MAAA,GAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC,MAAM,CAAA;AAChG,eAAe,GAAG,CAAC,KAAA,IAAS,KAAK,CAAC,QAAQ,CAAC;AAC3C,WAAW;AACX,OAAM;AACN,OAAO,KAAK,CAAC,CAAC,MAAM,KAAW;AAC/B,QAAQ,eAAe,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAA;AAClE,OAAO,CAAC,CAAA;AACR,GAAE;AACF;AACA;AACA;AACA;AACA,GAAU,MAAM,WAAW,CAAC,QAAQ,EAAyB;AAC7D,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;AACtD,GAAE;AACF;AACA;AACA;AACA;AACA,GAAU,MAAM,YAAY,CAAC,SAAS,EAA2B;AACjE;AACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,QAAS,IAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;AACpF,GAAE;AACF;AACA;AACA;AACA;AACA,GAAU,MAAM,WAAW,GAAkB;AAC7C,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAc,CAAC,KAAK,EAAS,QAAQ,EAAU,MAAM,KAAmB;AACjH,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE;AACpB,QAAQ,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;AACpC;AACA,QAAQ,KAAK,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAW;AAChE,UAAU,eAAe,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAA;AACxE,SAAS,CAAC,CAAA;AACV,aAAa;AACb,QAAQ,eAAe,MAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAA;AAChF,OAAM;AACN,KAAK,CAAC,CAAA;AACN,GAAE;AACF,CAAA,CAAA,OAAA,CAAA,YAAA,EAAA;;;;"}