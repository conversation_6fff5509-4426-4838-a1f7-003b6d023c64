{"version": 3, "file": "reportingobserver.js", "sources": ["../../../src/reportingobserver.ts"], "sourcesContent": ["import { captureMessage, convertIntegrationFnToClass, defineIntegration, getClient, withScope } from '@sentry/core';\nimport type { Client, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\nimport { GLOBAL_OBJ, supportsReportingObserver } from '@sentry/utils';\n\nconst WINDOW = GLOBAL_OBJ as typeof GLOBAL_OBJ & Window;\n\nconst INTEGRATION_NAME = 'ReportingObserver';\n\ninterface Report {\n  [key: string]: unknown;\n  type: ReportTypes;\n  url: string;\n  body?: ReportBody;\n}\n\ntype ReportTypes = 'crash' | 'deprecation' | 'intervention';\n\ntype ReportBody = CrashReportBody | DeprecationReportBody | InterventionReportBody;\n\ninterface CrashReportBody {\n  [key: string]: unknown;\n  crashId: string;\n  reason?: string;\n}\n\ninterface DeprecationReportBody {\n  [key: string]: unknown;\n  id: string;\n  anticipatedRemoval?: Date;\n  message: string;\n  sourceFile?: string;\n  lineNumber?: number;\n  columnNumber?: number;\n}\n\ninterface InterventionReportBody {\n  [key: string]: unknown;\n  id: string;\n  message: string;\n  sourceFile?: string;\n  lineNumber?: number;\n  columnNumber?: number;\n}\n\ninterface ReportingObserverOptions {\n  types?: ReportTypes[];\n}\n\nconst SETUP_CLIENTS = new WeakMap<Client, boolean>();\n\nconst _reportingObserverIntegration = ((options: ReportingObserverOptions = {}) => {\n  const types = options.types || ['crash', 'deprecation', 'intervention'];\n\n  /** Handler for the reporting observer. */\n  function handler(reports: Report[]): void {\n    if (!SETUP_CLIENTS.has(getClient() as Client)) {\n      return;\n    }\n\n    for (const report of reports) {\n      withScope(scope => {\n        scope.setExtra('url', report.url);\n\n        const label = `ReportingObserver [${report.type}]`;\n        let details = 'No details available';\n\n        if (report.body) {\n          // Object.keys doesn't work on ReportBody, as all properties are inheirted\n          const plainBody: {\n            [key: string]: unknown;\n          } = {};\n\n          // eslint-disable-next-line guard-for-in\n          for (const prop in report.body) {\n            plainBody[prop] = report.body[prop];\n          }\n\n          scope.setExtra('body', plainBody);\n\n          if (report.type === 'crash') {\n            const body = report.body as CrashReportBody;\n            // A fancy way to create a message out of crashId OR reason OR both OR fallback\n            details = [body.crashId || '', body.reason || ''].join(' ').trim() || details;\n          } else {\n            const body = report.body as DeprecationReportBody | InterventionReportBody;\n            details = body.message || details;\n          }\n        }\n\n        captureMessage(`${label}: ${details}`);\n      });\n    }\n  }\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      if (!supportsReportingObserver()) {\n        return;\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n      const observer = new (WINDOW as any).ReportingObserver(handler, {\n        buffered: true,\n        types,\n      });\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      observer.observe();\n    },\n\n    setup(client): void {\n      SETUP_CLIENTS.set(client, true);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const reportingObserverIntegration = defineIntegration(_reportingObserverIntegration);\n\n/**\n * Reporting API integration - https://w3c.github.io/reporting/\n * @deprecated Use `reportingObserverIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const ReportingObserver = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  reportingObserverIntegration,\n) as IntegrationClass<Integration & { setup: (client: Client) => void }> & {\n  new (options?: {\n    types?: ReportTypes[];\n  }): Integration;\n};\n"], "names": [], "mappings": ";;;AAIA,MAAM,MAAA,GAAS,UAAW,EAAA;AAC1B;AACA,MAAM,gBAAA,GAAmB,mBAAmB,CAAA;;AA0C5C,MAAM,aAAc,GAAE,IAAI,OAAO,EAAmB,CAAA;AACpD;AACA,MAAM,6BAAA,IAAiC,CAAC,OAAO,GAA6B,EAAE,KAAK;AACnF,EAAE,MAAM,KAAA,GAAQ,OAAO,CAAC,KAAA,IAAS,CAAC,OAAO,EAAE,aAAa,EAAE,cAAc,CAAC,CAAA;AACzE;AACA;AACA,EAAE,SAAS,OAAO,CAAC,OAAO,EAAkB;AAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAG,EAAU,EAAE;AACnD,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,KAAK,MAAM,MAAO,IAAG,OAAO,EAAE;AAClC,MAAM,SAAS,CAAC,KAAA,IAAS;AACzB,QAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAA;AACzC;AACA,QAAQ,MAAM,KAAM,GAAE,CAAC,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAC1D,QAAQ,IAAI,OAAQ,GAAE,sBAAsB,CAAA;AAC5C;AACA,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE;AACzB;AACA,UAAU,MAAM,SAAS;AACb;AACF,GAAI,EAAE,CAAA;AAChB;AACA;AACA,UAAU,KAAK,MAAM,IAAA,IAAQ,MAAM,CAAC,IAAI,EAAE;AAC1C,YAAY,SAAS,CAAC,IAAI,CAAE,GAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AAC/C,WAAU;AACV;AACA,UAAU,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;AAC3C;AACA,UAAU,IAAI,MAAM,CAAC,IAAK,KAAI,OAAO,EAAE;AACvC,YAAY,MAAM,IAAA,GAAO,MAAM,CAAC,IAAK,EAAA;AACrC;AACA,YAAY,OAAA,GAAU,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,MAAA,IAAU,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAG,IAAG,OAAO,CAAA;AACzF,iBAAiB;AACjB,YAAY,MAAM,IAAA,GAAO,MAAM,CAAC,IAAK,EAAA;AACrC,YAAY,UAAU,IAAI,CAAC,OAAA,IAAW,OAAO,CAAA;AAC7C,WAAU;AACV,SAAQ;AACR;AACA,QAAQ,cAAc,CAAC,CAAC,EAAA,KAAA,CAAA,EAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,gBAAA;AACA,IAAA,SAAA,GAAA;AACA,MAAA,IAAA,CAAA,yBAAA,EAAA,EAAA;AACA,QAAA,OAAA;AACA,OAAA;AACA;AACA;AACA,MAAA,MAAA,QAAA,GAAA,IAAA,CAAA,MAAA,GAAA,iBAAA,CAAA,OAAA,EAAA;AACA,QAAA,QAAA,EAAA,IAAA;AACA,QAAA,KAAA;AACA,OAAA,CAAA,CAAA;AACA;AACA;AACA,MAAA,QAAA,CAAA,OAAA,EAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,KAAA,CAAA,MAAA,EAAA;AACA,MAAA,aAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA,CAAA,CAAA,EAAA;AACA;AACA,MAAA,4BAAA,GAAA,iBAAA,CAAA,6BAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,iBAAA,GAAA,2BAAA;AACA,EAAA,gBAAA;AACA,EAAA,4BAAA;AACA,CAAA;;;;;;"}