{"version": 3, "file": "debug-build.js", "sources": ["../../../src/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": "AAEA;AACA;AACA;AACA;AACA;AACO,MAAM,WAAY,IAAE,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA;;;;"}