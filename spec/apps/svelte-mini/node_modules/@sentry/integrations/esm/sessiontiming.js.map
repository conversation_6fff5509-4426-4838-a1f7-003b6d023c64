{"version": 3, "file": "sessiontiming.js", "sources": ["../../../src/sessiontiming.ts"], "sourcesContent": ["import { convertIntegrationFnToClass, defineIntegration } from '@sentry/core';\nimport type { Event, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\n\nconst INTEGRATION_NAME = 'SessionTiming';\n\nconst _sessionTimingIntegration = (() => {\n  const startTime = Date.now();\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(event) {\n      const now = Date.now();\n\n      return {\n        ...event,\n        extra: {\n          ...event.extra,\n          ['session:start']: startTime,\n          ['session:duration']: now - startTime,\n          ['session:end']: now,\n        },\n      };\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const sessionTimingIntegration = defineIntegration(_sessionTimingIntegration);\n\n/**\n * This function adds duration since Sentry was initialized till the time event was sent.\n * @deprecated Use `sessionTimingIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const SessionTiming = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  sessionTimingIntegration,\n) as IntegrationClass<Integration & { processEvent: (event: Event) => Event }>;\n"], "names": [], "mappings": ";;AAGA,MAAM,gBAAA,GAAmB,eAAe,CAAA;AACxC;AACA,MAAM,yBAA0B,IAAG,MAAM;AACzC,EAAE,MAAM,SAAU,GAAE,IAAI,CAAC,GAAG,EAAE,CAAA;AAC9B;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,GAAI,GAAE,IAAI,CAAC,GAAG,EAAE,CAAA;AAC5B;AACA,MAAM,OAAO;AACb,QAAQ,GAAG,KAAK;AAChB,QAAQ,KAAK,EAAE;AACf,UAAU,GAAG,KAAK,CAAC,KAAK;AACxB,UAAU,CAAC,eAAe,GAAG,SAAS;AACtC,UAAU,CAAC,kBAAkB,GAAG,GAAA,GAAM,SAAS;AAC/C,UAAU,CAAC,aAAa,GAAG,GAAG;AAC9B,SAAS;AACT,OAAO,CAAA;AACP,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,wBAAyB,GAAE,iBAAiB,CAAC,yBAAyB,EAAC;AACpF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,aAAc,GAAE,2BAA2B;AACxD,EAAE,gBAAgB;AAClB,EAAE,wBAAwB;AAC1B,CAAE;;;;"}