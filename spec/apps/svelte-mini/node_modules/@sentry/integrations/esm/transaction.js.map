{"version": 3, "file": "transaction.js", "sources": ["../../../src/transaction.ts"], "sourcesContent": ["import { convertIntegrationFnToClass } from '@sentry/core';\nimport type { Event, Integration, IntegrationClass, IntegrationFn, StackFrame } from '@sentry/types';\n\nconst INTEGRATION_NAME = 'Transaction';\n\nconst transactionIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(event) {\n      const frames = _getFramesFromEvent(event);\n\n      // use for loop so we don't have to reverse whole frames array\n      for (let i = frames.length - 1; i >= 0; i--) {\n        const frame = frames[i];\n\n        if (frame.in_app === true) {\n          event.transaction = _getTransaction(frame);\n          break;\n        }\n      }\n\n      return event;\n    },\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Add node transaction to the event.\n * @deprecated This integration will be removed in v8.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const Transaction = convertIntegrationFnToClass(INTEGRATION_NAME, transactionIntegration) as IntegrationClass<\n  Integration & { processEvent: (event: Event) => Event }\n>;\n\nfunction _getFramesFromEvent(event: Event): StackFrame[] {\n  const exception = event.exception && event.exception.values && event.exception.values[0];\n  return (exception && exception.stacktrace && exception.stacktrace.frames) || [];\n}\n\nfunction _getTransaction(frame: StackFrame): string {\n  return frame.module || frame.function ? `${frame.module || '?'}/${frame.function || '?'}` : '<unknown>';\n}\n"], "names": [], "mappings": ";;AAGA,MAAM,gBAAA,GAAmB,aAAa,CAAA;AACtC;AACA,MAAM,sBAAuB,IAAG,MAAM;AACtC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,MAAM,MAAM,MAAO,GAAE,mBAAmB,CAAC,KAAK,CAAC,CAAA;AAC/C;AACA;AACA,MAAM,KAAK,IAAI,CAAE,GAAE,MAAM,CAAC,MAAA,GAAS,CAAC,EAAE,CAAE,IAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACnD,QAAQ,MAAM,KAAM,GAAE,MAAM,CAAC,CAAC,CAAC,CAAA;AAC/B;AACA,QAAQ,IAAI,KAAK,CAAC,MAAO,KAAI,IAAI,EAAE;AACnC,UAAU,KAAK,CAAC,WAAA,GAAc,eAAe,CAAC,KAAK,CAAC,CAAA;AACpD,UAAU,MAAK;AACf,SAAQ;AACR,OAAM;AACN;AACA,MAAM,OAAO,KAAK,CAAA;AAClB,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAc,2BAA2B,CAAC,gBAAgB,EAAE,sBAAsB,CAAE;;CAEjG;AACA;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAuB;AACzD,EAAE,MAAM,YAAY,KAAK,CAAC,SAAU,IAAG,KAAK,CAAC,SAAS,CAAC,MAAO,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;AAC1F,EAAE,OAAO,CAAC,SAAA,IAAa,SAAS,CAAC,UAAW,IAAG,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,EAAE,CAAA;AACjF,CAAA;AACA;AACA,SAAS,eAAe,CAAC,KAAK,EAAsB;AACpD,EAAE,OAAO,KAAK,CAAC,MAAO,IAAG,KAAK,CAAC,QAAA,GAAW,CAAC,EAAA,KAAA,CAAA,MAAA,IAAA,GAAA,CAAA,CAAA,EAAA,KAAA,CAAA,QAAA,IAAA,GAAA,CAAA,CAAA,GAAA,WAAA,CAAA;AACA;;;;"}