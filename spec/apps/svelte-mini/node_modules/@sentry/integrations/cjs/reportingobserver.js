Object.defineProperty(exports, '__esModule', { value: true });

const core = require('@sentry/core');
const utils = require('@sentry/utils');

const WINDOW = utils.GLOBAL_OBJ ;

const INTEGRATION_NAME = 'ReportingObserver';

const SETUP_CLIENTS = new WeakMap();

const _reportingObserverIntegration = ((options = {}) => {
  const types = options.types || ['crash', 'deprecation', 'intervention'];

  /** Handler for the reporting observer. */
  function handler(reports) {
    if (!SETUP_CLIENTS.has(core.getClient() )) {
      return;
    }

    for (const report of reports) {
      core.withScope(scope => {
        scope.setExtra('url', report.url);

        const label = `ReportingObserver [${report.type}]`;
        let details = 'No details available';

        if (report.body) {
          // Object.keys doesn't work on ReportBody, as all properties are inheirted
          const plainBody

 = {};

          // eslint-disable-next-line guard-for-in
          for (const prop in report.body) {
            plainBody[prop] = report.body[prop];
          }

          scope.setExtra('body', plainBody);

          if (report.type === 'crash') {
            const body = report.body ;
            // A fancy way to create a message out of crashId OR reason OR both OR fallback
            details = [body.crashId || '', body.reason || ''].join(' ').trim() || details;
          } else {
            const body = report.body ;
            details = body.message || details;
          }
        }

        core.captureMessage(`${label}: ${details}`);
      });
    }
  }

  return {
    name: INTEGRATION_NAME,
    setupOnce() {
      if (!utils.supportsReportingObserver()) {
        return;
      }

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
      const observer = new (WINDOW ).ReportingObserver(handler, {
        buffered: true,
        types,
      });

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      observer.observe();
    },

    setup(client) {
      SETUP_CLIENTS.set(client, true);
    },
  };
}) ;

const reportingObserverIntegration = core.defineIntegration(_reportingObserverIntegration);

/**
 * Reporting API integration - https://w3c.github.io/reporting/
 * @deprecated Use `reportingObserverIntegration()` instead.
 */
// eslint-disable-next-line deprecation/deprecation
const ReportingObserver = core.convertIntegrationFnToClass(
  INTEGRATION_NAME,
  reportingObserverIntegration,
)

;

exports.ReportingObserver = ReportingObserver;
exports.reportingObserverIntegration = reportingObserverIntegration;
//# sourceMappingURL=reportingobserver.js.map
