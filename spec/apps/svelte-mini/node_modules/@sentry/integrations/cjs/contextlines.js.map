{"version": 3, "file": "contextlines.js", "sources": ["../../../src/contextlines.ts"], "sourcesContent": ["import { convertIntegrationFnToClass, defineIntegration } from '@sentry/core';\nimport type { Event, Integration, IntegrationClass, IntegrationFn, StackFrame } from '@sentry/types';\nimport { GLOBAL_OBJ, addContextToFrame, stripUrlQueryAndFragment } from '@sentry/utils';\n\nconst WINDOW = GLOBAL_OBJ as typeof GLOBAL_OBJ & Window;\n\nconst DEFAULT_LINES_OF_CONTEXT = 7;\n\nconst INTEGRATION_NAME = 'ContextLines';\n\ninterface ContextLinesOptions {\n  /**\n   * Sets the number of context lines for each frame when loading a file.\n   * Defaults to 7.\n   *\n   * Set to 0 to disable loading and inclusion of source files.\n   **/\n  frameContextLines?: number;\n}\n\nconst _contextLinesIntegration = ((options: ContextLinesOptions = {}) => {\n  const contextLines = options.frameContextLines != null ? options.frameContextLines : DEFAULT_LINES_OF_CONTEXT;\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(event) {\n      return addSourceContext(event, contextLines);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const contextLinesIntegration = defineIntegration(_contextLinesIntegration);\n\n/**\n * Collects source context lines around the lines of stackframes pointing to JS embedded in\n * the current page's HTML.\n *\n * This integration DOES NOT work for stack frames pointing to JS files that are loaded by the browser.\n * For frames pointing to files, context lines are added during ingestion and symbolication\n * by attempting to download the JS files to the Sentry backend.\n *\n * Use this integration if you have inline JS code in HTML pages that can't be accessed\n * by our backend (e.g. due to a login-protected page).\n *\n * @deprecated Use `contextLinesIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const ContextLines = convertIntegrationFnToClass(INTEGRATION_NAME, contextLinesIntegration) as IntegrationClass<\n  Integration & { processEvent: (event: Event) => Event }\n> & { new (options?: { frameContextLines?: number }): Integration };\n\n/**\n * Processes an event and adds context lines.\n */\nfunction addSourceContext(event: Event, contextLines: number): Event {\n  const doc = WINDOW.document;\n  const htmlFilename = WINDOW.location && stripUrlQueryAndFragment(WINDOW.location.href);\n  if (!doc || !htmlFilename) {\n    return event;\n  }\n\n  const exceptions = event.exception && event.exception.values;\n  if (!exceptions || !exceptions.length) {\n    return event;\n  }\n\n  const html = doc.documentElement.innerHTML;\n  if (!html) {\n    return event;\n  }\n\n  const htmlLines = ['<!DOCTYPE html>', '<html>', ...html.split('\\n'), '</html>'];\n\n  exceptions.forEach(exception => {\n    const stacktrace = exception.stacktrace;\n    if (stacktrace && stacktrace.frames) {\n      stacktrace.frames = stacktrace.frames.map(frame =>\n        applySourceContextToFrame(frame, htmlLines, htmlFilename, contextLines),\n      );\n    }\n  });\n\n  return event;\n}\n\n/**\n * Only exported for testing\n */\nexport function applySourceContextToFrame(\n  frame: StackFrame,\n  htmlLines: string[],\n  htmlFilename: string,\n  linesOfContext: number,\n): StackFrame {\n  if (frame.filename !== htmlFilename || !frame.lineno || !htmlLines.length) {\n    return frame;\n  }\n\n  addContextToFrame(htmlLines, frame, linesOfContext);\n\n  return frame;\n}\n"], "names": ["GLOBAL_OBJ", "defineIntegration", "convertIntegrationFnToClass", "stripUrlQueryAndFragment", "addContextToFrame"], "mappings": ";;;;;AAIA,MAAM,MAAA,GAASA,gBAAW,EAAA;AAC1B;AACA,MAAM,wBAAA,GAA2B,CAAC,CAAA;AAClC;AACA,MAAM,gBAAA,GAAmB,cAAc,CAAA;;AAYvC,MAAM,wBAAA,IAA4B,CAAC,OAAO,GAAwB,EAAE,KAAK;AACzE,EAAE,MAAM,YAAA,GAAe,OAAO,CAAC,iBAAA,IAAqB,IAAA,GAAO,OAAO,CAAC,iBAAA,GAAoB,wBAAwB,CAAA;AAC/G;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,MAAM,OAAO,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;AAClD,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,uBAAwB,GAAEC,sBAAiB,CAAC,wBAAwB,EAAC;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,eAAeC,gCAA2B,CAAC,gBAAgB,EAAE,uBAAuB,CAAE;;CAEnG;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAS,YAAY,EAAiB;AACrE,EAAE,MAAM,GAAA,GAAM,MAAM,CAAC,QAAQ,CAAA;AAC7B,EAAE,MAAM,YAAA,GAAe,MAAM,CAAC,QAAS,IAAGC,8BAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;AACxF,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AAC7B,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,MAAM,UAAW,GAAE,KAAK,CAAC,SAAU,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAA;AAC9D,EAAE,IAAI,CAAC,UAAA,IAAc,CAAC,UAAU,CAAC,MAAM,EAAE;AACzC,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,MAAM,IAAK,GAAE,GAAG,CAAC,eAAe,CAAC,SAAS,CAAA;AAC5C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,MAAM,SAAU,GAAE,CAAC,iBAAiB,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAA;AACjF;AACA,EAAE,UAAU,CAAC,OAAO,CAAC,aAAa;AAClC,IAAI,MAAM,UAAA,GAAa,SAAS,CAAC,UAAU,CAAA;AAC3C,IAAI,IAAI,UAAA,IAAc,UAAU,CAAC,MAAM,EAAE;AACzC,MAAM,UAAU,CAAC,MAAA,GAAS,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,KAAM;AACtD,QAAQ,yBAAyB,CAAC,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC;AAC/E,OAAO,CAAA;AACP,KAAI;AACJ,GAAG,CAAC,CAAA;AACJ;AACA,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB;AACzC,EAAE,KAAK;AACP,EAAE,SAAS;AACX,EAAE,YAAY;AACd,EAAE,cAAc;AAChB,EAAc;AACd,EAAE,IAAI,KAAK,CAAC,QAAA,KAAa,YAAa,IAAG,CAAC,KAAK,CAAC,MAAO,IAAG,CAAC,SAAS,CAAC,MAAM,EAAE;AAC7E,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAEC,uBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,cAAc,CAAC,CAAA;AACrD;AACA,EAAE,OAAO,KAAK,CAAA;AACd;;;;;;"}