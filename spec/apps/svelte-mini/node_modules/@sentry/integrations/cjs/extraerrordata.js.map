{"version": 3, "file": "extraerrordata.js", "sources": ["../../../src/extraerrordata.ts"], "sourcesContent": ["import { convertIntegrationFnToClass, defineIntegration } from '@sentry/core';\nimport type {\n  Contexts,\n  Event,\n  EventHint,\n  ExtendedError,\n  Integration,\n  IntegrationClass,\n  IntegrationFn,\n} from '@sentry/types';\nimport { addNonEnumerableProperty, isError, isPlainObject, logger, normalize } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from './debug-build';\n\nconst INTEGRATION_NAME = 'ExtraErrorData';\n\ninterface ExtraErrorDataOptions {\n  /**\n   * The object depth up to which to capture data on error objects.\n   */\n  depth: number;\n\n  /**\n   * Whether to capture error causes.\n   *\n   * More information: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\n   */\n  captureErrorCause: boolean;\n}\n\nconst _extraErrorDataIntegration = ((options: Partial<ExtraErrorDataOptions> = {}) => {\n  const depth = options.depth || 3;\n\n  // TODO(v8): Flip the default for this option to true\n  const captureErrorCause = options.captureErrorCause || false;\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(event, hint) {\n      return _enhanceEventWithErrorData(event, hint, depth, captureErrorCause);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const extraErrorDataIntegration = defineIntegration(_extraErrorDataIntegration);\n\n/**\n * Extract additional data for from original exceptions.\n * @deprecated Use `extraErrorDataIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const ExtraErrorData = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  extraErrorDataIntegration,\n) as IntegrationClass<Integration & { processEvent: (event: Event, hint: EventHint) => Event }> & {\n  new (\n    options?: Partial<{\n      depth: number;\n      captureErrorCause: boolean;\n    }>,\n  ): Integration;\n};\n\nfunction _enhanceEventWithErrorData(\n  event: Event,\n  hint: EventHint = {},\n  depth: number,\n  captureErrorCause: boolean,\n): Event {\n  if (!hint.originalException || !isError(hint.originalException)) {\n    return event;\n  }\n  const exceptionName = (hint.originalException as ExtendedError).name || hint.originalException.constructor.name;\n\n  const errorData = _extractErrorData(hint.originalException as ExtendedError, captureErrorCause);\n\n  if (errorData) {\n    const contexts: Contexts = {\n      ...event.contexts,\n    };\n\n    const normalizedErrorData = normalize(errorData, depth);\n\n    if (isPlainObject(normalizedErrorData)) {\n      // We mark the error data as \"already normalized\" here, because we don't want other normalization procedures to\n      // potentially truncate the data we just already normalized, with a certain depth setting.\n      addNonEnumerableProperty(normalizedErrorData, '__sentry_skip_normalization__', true);\n      contexts[exceptionName] = normalizedErrorData;\n    }\n\n    return {\n      ...event,\n      contexts,\n    };\n  }\n\n  return event;\n}\n\n/**\n * Extract extra information from the Error object\n */\nfunction _extractErrorData(error: ExtendedError, captureErrorCause: boolean): Record<string, unknown> | null {\n  // We are trying to enhance already existing event, so no harm done if it won't succeed\n  try {\n    const nativeKeys = [\n      'name',\n      'message',\n      'stack',\n      'line',\n      'column',\n      'fileName',\n      'lineNumber',\n      'columnNumber',\n      'toJSON',\n    ];\n\n    const extraErrorInfo: Record<string, unknown> = {};\n\n    // We want only enumerable properties, thus `getOwnPropertyNames` is redundant here, as we filter keys anyway.\n    for (const key of Object.keys(error)) {\n      if (nativeKeys.indexOf(key) !== -1) {\n        continue;\n      }\n      const value = error[key];\n      extraErrorInfo[key] = isError(value) ? value.toString() : value;\n    }\n\n    // Error.cause is a standard property that is non enumerable, we therefore need to access it separately.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error/cause\n    if (captureErrorCause && error.cause !== undefined) {\n      extraErrorInfo.cause = isError(error.cause) ? error.cause.toString() : error.cause;\n    }\n\n    // Check if someone attached `toJSON` method to grab even more properties (eg. axios is doing that)\n    if (typeof error.toJSON === 'function') {\n      const serializedError = error.toJSON() as Record<string, unknown>;\n\n      for (const key of Object.keys(serializedError)) {\n        const value = serializedError[key];\n        extraErrorInfo[key] = isError(value) ? value.toString() : value;\n      }\n    }\n\n    return extraErrorInfo;\n  } catch (oO) {\n    DEBUG_BUILD && logger.error('Unable to extract extra data from the Error object:', oO);\n  }\n\n  return null;\n}\n"], "names": ["defineIntegration", "convertIntegrationFnToClass", "isError", "normalize", "isPlainObject", "addNonEnumerableProperty", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;AAcA,MAAM,gBAAA,GAAmB,gBAAgB,CAAA;;AAgBzC,MAAM,0BAAA,IAA8B,CAAC,OAAO,GAAmC,EAAE,KAAK;AACtF,EAAE,MAAM,KAAM,GAAE,OAAO,CAAC,KAAA,IAAS,CAAC,CAAA;AAClC;AACA;AACA,EAAE,MAAM,iBAAkB,GAAE,OAAO,CAAC,iBAAA,IAAqB,KAAK,CAAA;AAC9D;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE;AAC9B,MAAM,OAAO,0BAA0B,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAA;AAC9E,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,yBAA0B,GAAEA,sBAAiB,CAAC,0BAA0B,EAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAe,GAAEC,gCAA2B;AACzD,EAAE,gBAAgB;AAClB,EAAE,yBAAyB;AAC3B,CAAE;;CAOF;AACA;AACA,SAAS,0BAA0B;AACnC,EAAE,KAAK;AACP,EAAE,IAAI,GAAc,EAAE;AACtB,EAAE,KAAK;AACP,EAAE,iBAAiB;AACnB,EAAS;AACT,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAkB,IAAG,CAACC,aAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;AACnE,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF,EAAE,MAAM,aAAc,GAAE,CAAC,IAAI,CAAC,iBAAkB,GAAkB,IAAA,IAAQ,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAA;AACjH;AACA,EAAE,MAAM,SAAU,GAAE,iBAAiB,CAAC,IAAI,CAAC,iBAAA,GAAoC,iBAAiB,CAAC,CAAA;AACjG;AACA,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,MAAM,QAAQ,GAAa;AAC/B,MAAM,GAAG,KAAK,CAAC,QAAQ;AACvB,KAAK,CAAA;AACL;AACA,IAAI,MAAM,sBAAsBC,eAAS,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;AAC3D;AACA,IAAI,IAAIC,mBAAa,CAAC,mBAAmB,CAAC,EAAE;AAC5C;AACA;AACA,MAAMC,8BAAwB,CAAC,mBAAmB,EAAE,+BAA+B,EAAE,IAAI,CAAC,CAAA;AAC1F,MAAM,QAAQ,CAAC,aAAa,CAAA,GAAI,mBAAmB,CAAA;AACnD,KAAI;AACJ;AACA,IAAI,OAAO;AACX,MAAM,GAAG,KAAK;AACd,MAAM,QAAQ;AACd,KAAK,CAAA;AACL,GAAE;AACF;AACA,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA;AACA;AACA;AACA,SAAS,iBAAiB,CAAC,KAAK,EAAiB,iBAAiB,EAA2C;AAC7G;AACA,EAAE,IAAI;AACN,IAAI,MAAM,aAAa;AACvB,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,MAAM,UAAU;AAChB,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,QAAQ;AACd,KAAK,CAAA;AACL;AACA,IAAI,MAAM,cAAc,GAA4B,EAAE,CAAA;AACtD;AACA;AACA,IAAI,KAAK,MAAM,GAAI,IAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC1C,MAAM,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAE,KAAI,CAAC,CAAC,EAAE;AAC1C,QAAQ,SAAQ;AAChB,OAAM;AACN,MAAM,MAAM,KAAM,GAAE,KAAK,CAAC,GAAG,CAAC,CAAA;AAC9B,MAAM,cAAc,CAAC,GAAG,CAAE,GAAEH,aAAO,CAAC,KAAK,CAAE,GAAE,KAAK,CAAC,QAAQ,EAAC,GAAI,KAAK,CAAA;AACrE,KAAI;AACJ;AACA;AACA;AACA,IAAI,IAAI,iBAAkB,IAAG,KAAK,CAAC,KAAA,KAAU,SAAS,EAAE;AACxD,MAAM,cAAc,CAAC,KAAM,GAAEA,aAAO,CAAC,KAAK,CAAC,KAAK,CAAA,GAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAG,GAAE,KAAK,CAAC,KAAK,CAAA;AACxF,KAAI;AACJ;AACA;AACA,IAAI,IAAI,OAAO,KAAK,CAAC,MAAO,KAAI,UAAU,EAAE;AAC5C,MAAM,MAAM,eAAgB,GAAE,KAAK,CAAC,MAAM,EAAG,EAAA;AAC7C;AACA,MAAM,KAAK,MAAM,GAAI,IAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AACtD,QAAQ,MAAM,KAAM,GAAE,eAAe,CAAC,GAAG,CAAC,CAAA;AAC1C,QAAQ,cAAc,CAAC,GAAG,CAAE,GAAEA,aAAO,CAAC,KAAK,CAAE,GAAE,KAAK,CAAC,QAAQ,EAAC,GAAI,KAAK,CAAA;AACvE,OAAM;AACN,KAAI;AACJ;AACA,IAAI,OAAO,cAAc,CAAA;AACzB,GAAI,CAAA,OAAO,EAAE,EAAE;AACf,IAAII,sBAAA,IAAeC,YAAM,CAAC,KAAK,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAAA;AAC1F,GAAE;AACF;AACA,EAAE,OAAO,IAAI,CAAA;AACb;;;;;"}